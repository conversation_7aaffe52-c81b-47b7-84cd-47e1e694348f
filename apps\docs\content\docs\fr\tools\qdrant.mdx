---
title: Qdrant
description: Utilisez la base de données vectorielle Qdrant
---

import { BlockInfoCard } from "@/components/ui/block-info-card"

<BlockInfoCard 
  type="qdrant"
  color="#1A223F"
  icon={true}
  iconSvg={`<svg className="block-icon"  fill='none' viewBox='0 0 49 56' xmlns='http://www.w3.org/2000/svg'>
      <g clipPath='url(#b)'>
        <path
          d='m38.489 51.477-1.1167-30.787-2.0223-8.1167 13.498 1.429v37.242l-8.2456 4.7589-2.1138-4.5259z'
          clipRule='evenodd'
          fill='#24386C'
          fillRule='evenodd'
        />
        <path
          d='m48.847 14-8.2457 4.7622-17.016-3.7326-19.917 8.1094-3.3183-9.139 12.122-7 12.126-7 12.123 7 12.126 7z'
          clipRule='evenodd'
          fill='#7589BE'
          fillRule='evenodd'
        />
        <path
          d='m0.34961 13.999 8.2457 4.7622 4.7798 14.215 16.139 12.913-4.9158 10.109-12.126-7.0004-12.123-7v-28z'
          clipRule='evenodd'
          fill='#B2BFE8'
          fillRule='evenodd'
        />
        <path
          d='m30.066 38.421-5.4666 8.059v9.5207l7.757-4.4756 3.9968-5.9681'
          clipRule='evenodd'
          fill='#24386C'
          fillRule='evenodd'
        />
        <path
          d='m24.602 36.962-7.7603-13.436 1.6715-4.4531 6.3544-3.0809 7.488 7.5343-7.7536 13.436z'
          clipRule='evenodd'
          fill='#7589BE'
          fillRule='evenodd'
        />
        <path
          d='m16.843 23.525 7.7569 4.4756v8.9585l-7.1741 0.3087-4.3397-5.5412 3.7569-8.2016z'
          clipRule='evenodd'
          fill='#B2BFE8'
          fillRule='evenodd'
        />
        <path
          d='m24.6 28 7.757-4.4752 5.2792 8.7903-6.3886 5.2784-6.6476-0.6346v-8.9589z'
          clipRule='evenodd'
          fill='#24386C'
          fillRule='evenodd'
        />
        <path
          d='m32.355 51.524 8.2457 4.476v-37.238l-8.0032-4.6189-7.9995-4.6189-8.0031 4.6189-7.9995 4.6189v18.479l7.9995 4.6189 8.0031 4.6193 7.757-4.4797v9.5244zm0-19.045-7.757 4.4793-7.7569-4.4793v-8.9549l7.7569-4.4792 7.757 4.4792v8.9549z'
          clipRule='evenodd'
          fill='#DC244C'
          fillRule='evenodd'
        />
        <path d='m24.603 46.483v-9.5222l-7.7166-4.4411v9.5064l7.7166 4.4569z' fill='url(#a)' />
      </g>
      <defs>
        <linearGradient
          id='a'
          x1='23.18'
          x2='15.491'
          y1='38.781'
          y2='38.781'
          gradientUnits='userSpaceOnUse'
        >
          <stop stopColor='#FF3364' offset='0' />
          <stop stopColor='#C91540' stopOpacity='0' offset='1' />
        </linearGradient>
        <clipPath id='b'>
          <rect transform='translate(.34961)'   fill='#fff' />
        </clipPath>
      </defs>
    </svg>`}
/>

{/* MANUAL-CONTENT-START:intro */}
[Qdrant](https://qdrant.tech) est une base de données vectorielle open-source conçue pour le stockage, la gestion et la récupération efficaces d'embeddings vectoriels de haute dimension. Qdrant permet une recherche sémantique rapide et évolutive, ce qui en fait un choix idéal pour les applications d'IA nécessitant une recherche par similarité, des systèmes de recommandation et une récupération d'informations contextuelles.

Avec Qdrant, vous pouvez :

- **Stocker des embeddings vectoriels** : gérer et persister efficacement des vecteurs de haute dimension à grande échelle
- **Effectuer une recherche de similarité sémantique** : trouver en temps réel les vecteurs les plus similaires à un vecteur de requête
- **Filtrer et organiser les données** : utiliser un filtrage avancé pour affiner les résultats de recherche en fonction des métadonnées ou des charges utiles
- **Récupérer des points spécifiques** : extraire des vecteurs et leurs charges utiles associées par ID
- **Évoluer en toute transparence** : gérer de grandes collections et des charges de travail à haut débit

Dans Sim, l'intégration de Qdrant permet à vos agents d'interagir avec Qdrant de manière programmatique dans le cadre de leurs flux de travail. Les opérations prises en charge comprennent :

- **Upsert** : Insérer ou mettre à jour des points (vecteurs et charges utiles) dans une collection Qdrant
- **Search** : Effectuer une recherche de similarité pour trouver les vecteurs les plus similaires à un vecteur de requête donné, avec filtrage optionnel et personnalisation des résultats
- **Fetch** : Récupérer des points spécifiques d'une collection par leurs identifiants, avec options pour inclure les charges utiles et les vecteurs

Cette intégration permet à vos agents d'exploiter de puissantes capacités de recherche et de gestion vectorielles, permettant des scénarios d'automatisation avancés tels que la recherche sémantique, la recommandation et la récupération contextuelle. En connectant Sim avec Qdrant, vous pouvez créer des agents qui comprennent le contexte, récupèrent des informations pertinentes à partir de grands ensembles de données et fournissent des réponses plus intelligentes et personnalisées, le tout sans gérer une infrastructure complexe.
{/* MANUAL-CONTENT-END */}

## Instructions d'utilisation

Stockez, recherchez et récupérez des embeddings vectoriels à l'aide de Qdrant. Effectuez des recherches de similarité sémantique et gérez vos collections de vecteurs.

## Outils

### `qdrant_upsert_points`

Insérer ou mettre à jour des points dans une collection Qdrant

#### Entrée

| Paramètre | Type | Obligatoire | Description |
| --------- | ---- | ----------- | ----------- |
| `url` | string | Oui | URL de base de Qdrant |
| `apiKey` | string | Non | Clé API Qdrant \(facultative\) |
| `collection` | string | Oui | Nom de la collection |
| `points` | array | Oui | Tableau de points à upsert |

#### Sortie

| Paramètre | Type | Description |
| --------- | ---- | ----------- |
| `status` | string | Statut de l'opération d'upsert |
| `data` | object | Données de résultat de l'opération d'upsert |

### `qdrant_search_vector`

Rechercher des vecteurs similaires dans une collection Qdrant

#### Entrée

| Paramètre | Type | Obligatoire | Description |
| --------- | ---- | ---------- | ----------- |
| `url` | chaîne | Oui | URL de base Qdrant |
| `apiKey` | chaîne | Non | Clé API Qdrant \(facultative\) |
| `collection` | chaîne | Oui | Nom de la collection |
| `vector` | tableau | Oui | Vecteur à rechercher |
| `limit` | nombre | Non | Nombre de résultats à retourner |
| `filter` | objet | Non | Filtre à appliquer à la recherche |
| `with_payload` | booléen | Non | Inclure la charge utile dans la réponse |
| `with_vector` | booléen | Non | Inclure le vecteur dans la réponse |

#### Sortie

| Paramètre | Type | Description |
| --------- | ---- | ----------- |
| `data` | tableau | Résultats de recherche de vecteurs avec ID, score, charge utile et données vectorielles optionnelles |
| `status` | chaîne | Statut de l'opération de recherche |

### `qdrant_fetch_points`

Récupérer des points par ID depuis une collection Qdrant

#### Entrée

| Paramètre | Type | Obligatoire | Description |
| --------- | ---- | ---------- | ----------- |
| `url` | chaîne | Oui | URL de base Qdrant |
| `apiKey` | chaîne | Non | Clé API Qdrant \(facultative\) |
| `collection` | chaîne | Oui | Nom de la collection |
| `ids` | tableau | Oui | Tableau d'identifiants de points à récupérer |
| `with_payload` | booléen | Non | Inclure la charge utile dans la réponse |
| `with_vector` | booléen | Non | Inclure le vecteur dans la réponse |

#### Sortie

| Paramètre | Type | Description |
| --------- | ---- | ----------- |
| `data` | tableau | Points récupérés avec ID, charge utile et données vectorielles optionnelles |
| `status` | chaîne | Statut de l'opération de récupération |

## Notes

- Catégorie : `tools`
- Type : `qdrant`
