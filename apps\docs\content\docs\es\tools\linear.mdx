---
title: Linear
description: <PERSON> y crea incidencias en Linear
---

import { BlockInfoCard } from "@/components/ui/block-info-card"

<BlockInfoCard 
  type="linear"
  color="#5E6AD2"
  icon={true}
  iconSvg={`<svg className="block-icon"
      
      xmlns='http://www.w3.org/2000/svg'
      fill='currentColor'
      
      
      viewBox='0 0 100 100'
    >
      <path
        fill='currentColor'
        d='M1.22541 61.5228c-.2225-.9485.90748-1.5459 1.59638-.857L39.3342 97.1782c.6889.6889.0915 1.8189-.857 1.5964C20.0515 94.4522 5.54779 79.9485 1.22541 61.5228ZM.00189135 46.8891c-.01764375.2833.08887215.5599.28957165.7606L52.3503 99.7085c.2007.2007.4773.3075.7606.2896 2.3692-.1476 4.6938-.46 6.9624-.9259.7645-.157 1.0301-1.0963.4782-1.6481L2.57595 39.4485c-.55186-.5519-1.49117-.2863-1.648174.4782-.465915 2.2686-.77832 4.5932-.92588465 6.9624ZM4.21093 29.7054c-.16649.3738-.08169.8106.20765 1.1l64.77602 64.776c.2894.2894.7262.3742 1.1.2077 1.7861-.7956 3.5171-1.6927 5.1855-2.684.5521-.328.6373-1.0867.1832-1.5407L8.43566 24.3367c-.45409-.4541-1.21271-.3689-1.54074.1832-.99132 1.6684-1.88843 3.3994-2.68399 5.1855ZM12.6587 18.074c-.3701-.3701-.393-.9637-.0443-1.3541C21.7795 6.45931 35.1114 0 49.9519 0 77.5927 0 100 22.4073 100 50.0481c0 14.8405-6.4593 28.1724-16.7199 37.3375-.3903.3487-.984.3258-1.3542-.0443L12.6587 18.074Z'
      />
    </svg>`}
/>

{/* MANUAL-CONTENT-START:intro */}
[Linear](https://linear.app) es una plataforma líder de gestión de proyectos y seguimiento de incidencias que ayuda a los equipos a planificar, rastrear y gestionar su trabajo de manera efectiva. Como herramienta moderna de gestión de proyectos, Linear se ha vuelto cada vez más popular entre los equipos de desarrollo de software y profesionales de gestión de proyectos por su interfaz simplificada y potentes funcionalidades.

Linear proporciona un conjunto completo de herramientas para gestionar proyectos complejos a través de su sistema de flujo de trabajo flexible y personalizable. Con sus sólidas capacidades de API e integración, Linear permite a los equipos optimizar sus procesos de desarrollo y mantener una clara visibilidad del progreso del proyecto.

Las características principales de Linear incluyen:

- Gestión ágil de proyectos: Soporte para metodologías Scrum y Kanban con tableros y flujos de trabajo personalizables
- Seguimiento de incidencias: Sistema sofisticado de seguimiento para bugs, historias, épicas y tareas con informes detallados
- Automatización de flujos de trabajo: Potentes reglas de automatización para optimizar tareas y procesos repetitivos
- Búsqueda avanzada: Capacidades complejas de filtrado e informes para una gestión eficiente de incidencias

En Sim, la integración con Linear permite a tus agentes interactuar sin problemas con tu flujo de trabajo de gestión de proyectos. Esto crea oportunidades para la creación automatizada de incidencias, actualizaciones y seguimiento como parte de tus flujos de trabajo de IA. La integración permite a los agentes leer incidencias existentes y crear nuevas de forma programática, facilitando tareas automatizadas de gestión de proyectos y asegurando que la información importante sea debidamente rastreada y documentada. Al conectar Sim con Linear, puedes construir agentes inteligentes que mantengan la visibilidad del proyecto mientras automatizan tareas rutinarias de gestión, mejorando la productividad del equipo y asegurando un seguimiento consistente del proyecto.
{/* MANUAL-CONTENT-END */}

## Instrucciones de uso

Integra con Linear para obtener, filtrar y crear incidencias directamente desde tu flujo de trabajo.

## Herramientas

### `linear_read_issues`

Obtener y filtrar incidencias de Linear

#### Entrada

| Parámetro | Tipo | Obligatorio | Descripción |
| --------- | ---- | ----------- | ----------- |
| `teamId` | string | Sí | ID del equipo de Linear |
| `projectId` | string | Sí | ID del proyecto de Linear |

#### Salida

| Parámetro | Tipo | Descripción |
| --------- | ---- | ----------- |
| `issues` | array | Array de incidencias del equipo y proyecto de Linear especificados, cada una contiene id, título, descripción, estado, teamId y projectId |

### `linear_create_issue`

Crear una nueva incidencia en Linear

#### Entrada

| Parámetro | Tipo | Obligatorio | Descripción |
| --------- | ---- | ----------- | ----------- |
| `teamId` | string | Sí | ID del equipo de Linear |
| `projectId` | string | Sí | ID del proyecto de Linear |
| `title` | string | Sí | Título de la incidencia |
| `description` | string | No | Descripción de la incidencia |

#### Salida

| Parámetro | Tipo | Descripción |
| --------- | ---- | ----------- |
| `issue` | object | La incidencia creada que contiene id, título, descripción, estado, teamId y projectId |

## Notas

- Categoría: `tools`
- Tipo: `linear`
