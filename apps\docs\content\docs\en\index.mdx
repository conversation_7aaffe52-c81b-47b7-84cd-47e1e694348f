---
title: Documentation
---

import { Card, Cards } from 'fumadocs-ui/components/card'

# Sim Documentation

Welcome to <PERSON>m, a visual workflow builder for AI applications. Build powerful AI agents, automation workflows, and data processing pipelines by connecting blocks on a canvas.

## Quick Start

<Cards>
  <Card title="Introduction" href="/introduction">
    Learn what you can build with Sim
  </Card>
  <Card title="Getting Started" href="/getting-started">
    Create your first workflow in 10 minutes
  </Card>
  <Card title="Workflow Blocks" href="/blocks">
    Learn about the building blocks
  </Card>
  <Card title="Tools & Integrations" href="/tools">
    Explore 80+ built-in integrations
  </Card>
</Cards>

## Core Concepts

<Cards>
  <Card title="Connections" href="/connections">
    Understand how data flows between blocks
  </Card>
  <Card title="Variables" href="/variables">
    Work with workflow and environment variables
  </Card>
  <Card title="Execution" href="/execution">
    Monitor workflow runs and manage costs
  </Card>
  <Card title="Triggers" href="/triggers">
    Start workflows via API, webhooks, or schedules
  </Card>
</Cards>

## Advanced Features

<Cards>
  <Card title="Team Management" href="/permissions/roles-and-permissions">
    Set up workspace roles and permissions
  </Card>
  <Card title="YAML Configuration" href="/yaml">
    Define workflows as code
  </Card>
  <Card title="MCP Integration" href="/mcp">
    Connect external services with Model Context Protocol
  </Card>
  <Card title="SDKs" href="/sdks">
    Integrate Sim into your applications
  </Card>
</Cards>