---
title: Condition Block YAML Schema
description: YAML configuration reference for Condition blocks
---

## Schema Definition

```yaml
type: object
required:
  - type
  - name
  - inputs
  - connections
properties:
  type:
    type: string
    enum: [condition]
    description: Block type identifier
  name:
    type: string
    description: Display name for this condition block
  inputs:
    type: object
    required:
      - conditions
    properties:
      conditions:
        type: object
        description: Conditional expressions and their logic
        properties:
          if:
            type: string
            description: Primary condition expression (boolean)
          else-if:
            type: string
            description: Secondary condition expression (optional)
          else-if-2:
            type: string
            description: Third condition expression (optional)
          else-if-3:
            type: string
            description: Fourth condition expression (optional)
          # Additional else-if-N conditions can be added as needed
          else:
            type: boolean
            description: Default fallback condition (optional)
            default: true
  connections:
    type: object
    required:
      - conditions
    properties:
      conditions:
        type: object
        description: Target blocks for each condition outcome
        properties:
          if:
            type: string
            description: Target block ID when 'if' condition is true
          else-if:
            type: string
            description: Target block ID when 'else-if' condition is true
          else-if-2:
            type: string
            description: Target block ID when 'else-if-2' condition is true
          else-if-3:
            type: string
            description: Target block ID when 'else-if-3' condition is true
          # Additional else-if-N connections can be added as needed
          else:
            type: string
            description: Target block ID when no conditions match
```

## Connection Configuration

Unlike other blocks, conditions use branching connections based on condition outcomes:

```yaml
connections:
  conditions:
    if: <string>                        # Target block ID when primary condition is true
    else-if: <string>                   # Target block ID when secondary condition is true (optional)
    else-if-2: <string>                 # Target block ID when third condition is true (optional)
    else-if-3: <string>                 # Target block ID when fourth condition is true (optional)
    # Additional else-if-N connections can be added as needed
    else: <string>                      # Target block ID when no conditions match (optional)
```

## Examples

### Simple If-Else

```yaml
status-check:
  type: condition
  name: "Status Check"
  inputs:
    conditions:
      if: <start.status> === "approved"
      else: true
  connections:
    conditions:
      if: send-approval-email
      else: send-rejection-email
```

### Multiple Conditions

```yaml
user-routing:
  type: condition
  name: "User Type Router"
  inputs:
    conditions:
      if: <start.user_type> === "admin"
      else-if: <start.user_type> === "premium"
      else-if-2: <start.user_type> === "basic"
      else: true
  connections:
    conditions:
      if: admin-dashboard
      else-if: premium-features
      else-if-2: basic-features
      else: registration-flow
```

### Numeric Comparisons

```yaml
score-evaluation:
  type: condition
  name: "Score Evaluation"
  inputs:
    conditions:
      if: <agent.score> >= 90
      else-if: <agent.score> >= 70
      else-if-2: <agent.score> >= 50
      else: true
  connections:
    conditions:
      if: excellent-response
      else-if: good-response
      else-if-2: average-response
      else: poor-response
```

### Complex Logic

```yaml
eligibility-check:
  type: condition
  name: "Eligibility Check"
  inputs:
    conditions:
      if: <start.age> >= 18 && <start.verified> === true
      else-if: <start.age> >= 16 && <start.parent_consent> === true
      else: true
  connections:
    conditions:
      if: full-access
      else-if: limited-access
      else: access-denied
``` 