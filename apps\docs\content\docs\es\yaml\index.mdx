---
title: Referencia de flujos de trabajo YAML
description: Guía completa para escribir flujos de trabajo YAML en Sim
---

import { Card, Cards } from "fumadocs-ui/components/card";
import { Step, Steps } from "fumadocs-ui/components/steps";
import { Tab, Tabs } from "fumadocs-ui/components/tabs";

Los flujos de trabajo YAML proporcionan una forma potente de definir, versionar y compartir configuraciones de flujos de trabajo en Sim. Esta guía de referencia cubre la sintaxis completa de YAML, esquemas de bloques y mejores prácticas para crear flujos de trabajo robustos.

## Inicio rápido

Cada flujo de trabajo de Sim sigue esta estructura básica:

```yaml
version: '1.0'
blocks:
  start:
    type: starter
    name: Start
    inputs:
      startWorkflow: manual
    connections:
      success: agent-1

  agent-1:
    type: agent
    name: "AI Assistant"
    inputs:
      systemPrompt: "You are a helpful assistant."
      userPrompt: 'Hi'
      model: gpt-4o
      apiKey: '{{OPENAI_API_KEY}}'
```

## Conceptos fundamentales

<Steps>
  <Step>
    <strong>Declaración de versión</strong>: Debe ser exactamente `version: '1.0'` (con comillas)
  </Step>
  <Step>
    <strong>Estructura de bloques</strong>: Todos los bloques del flujo de trabajo se definen bajo la clave `blocks`
  </Step>
  <Step>
    <strong>Referencias de bloques</strong>: Usa nombres de bloques en minúsculas sin espacios (por ejemplo, `<aiassistant.content>`)
  </Step>
  <Step>
    <strong>Variables de entorno</strong>: Referencia con dobles llaves `{{VARIABLE_NAME}}`
  </Step>
</Steps>

## Tipos de bloques

Sim admite varios tipos de bloques principales, cada uno con esquemas YAML específicos:

<Cards>
  <Card title="Bloque de inicio" href="/yaml/blocks/starter">
    Punto de entrada del flujo de trabajo con soporte para activadores manuales, webhooks y programados
  </Card>
  <Card title="Bloque de agente" href="/yaml/blocks/agent">
    Procesamiento impulsado por IA con soporte para herramientas y salida estructurada
  </Card>
  <Card title="Bloque de función" href="/yaml/blocks/function">
    Ejecución de código JavaScript/TypeScript personalizado
  </Card>
  <Card title="Bloque de API" href="/yaml/blocks/api">
    Solicitudes HTTP a servicios externos
  </Card>
  <Card title="Bloque de condición" href="/yaml/blocks/condition">
    Ramificación condicional basada en expresiones booleanas
  </Card>
  <Card title="Bloque de enrutador" href="/yaml/blocks/router">
    Enrutamiento inteligente impulsado por IA a múltiples rutas
  </Card>
  <Card title="Bloque de bucle" href="/yaml/blocks/loop">
    Procesamiento iterativo con bucles for y forEach
  </Card>
  <Card title="Bloque paralelo" href="/yaml/blocks/parallel">
    Ejecución concurrente en múltiples instancias
  </Card>
  <Card title="Bloque de webhook" href="/yaml/blocks/webhook">
    Activadores de webhook para integraciones externas
  </Card>
  <Card title="Bloque evaluador" href="/yaml/blocks/evaluator">
    Validación de salidas según criterios y métricas definidos
  </Card>
  <Card title="Bloque de flujo de trabajo" href="/yaml/blocks/workflow">
    Ejecuta otros flujos de trabajo como componentes reutilizables
  </Card>
  <Card title="Bloque de respuesta" href="/yaml/blocks/response">
    Formateo de salida final del flujo de trabajo
  </Card>
</Cards>

## Sintaxis de referencia de bloques

El aspecto más crítico de los flujos de trabajo YAML es entender cómo referenciar datos entre bloques:

### Reglas básicas

1. **Usa el nombre del bloque** (no el ID del bloque) convertido a minúsculas con espacios eliminados
2. **Añade la propiedad apropiada** (.content para agentes, .output para herramientas)
3. **Cuando uses chat, referencia el bloque inicial** como `<start.input>`

### Ejemplos

```yaml
# Block definitions
email-processor:
  type: agent
  name: "Email Agent"
  # ... configuration

data-formatter:
  type: function
  name: "Data Agent"
  # ... configuration

# Referencing their outputs
next-block:
  type: agent
  name: "Next Step"
  inputs:
    userPrompt: |
      Process this email: <emailagent.content>
      Use this formatted data: <dataagent.output>
      Original input: <start.input>
```

### Casos especiales

- **Variables de bucle**: `<loop.index>`, `<loop.currentItem>`, `<loop.items>`
- **Variables paralelas**: `<parallel.index>`, `<parallel.currentItem>`

## Variables de entorno

Usa variables de entorno para datos sensibles como claves API:

```yaml
inputs:
  apiKey: '{{OPENAI_API_KEY}}'
  database: '{{DATABASE_URL}}'
  token: '{{SLACK_BOT_TOKEN}}'
```

## Mejores prácticas

- **Mantén los nombres de bloques legibles**: "Procesador de correo" para mostrar en la interfaz
- **Referencia variables de entorno**: Nunca codifiques claves API directamente
- **Estructura para legibilidad**: Agrupa bloques relacionados de manera lógica
- **Prueba incrementalmente**: Construye flujos de trabajo paso a paso

## Próximos pasos

- [Sintaxis de referencia de bloques](/yaml/block-reference) - Reglas de referencia detalladas
- [Esquemas completos de bloques](/yaml/blocks) - Todos los tipos de bloques disponibles
- [Ejemplos de flujos de trabajo](/yaml/examples) - Patrones de flujos de trabajo del mundo real