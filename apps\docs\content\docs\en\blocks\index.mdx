---
title: Blocks
description: The building components of your AI workflows
---

import { Card, Cards } from 'fumadocs-ui/components/card'
import { Step, Steps } from 'fumadocs-ui/components/steps'
import { Tab, Tabs } from 'fumadocs-ui/components/tabs'
import { Video } from '@/components/ui/video'

Blocks are the building components you connect together to create AI workflows. Think of them as specialized modules that each handle a specific task—from chatting with AI models to making API calls or processing data.

<div className="w-full max-w-2xl mx-auto overflow-hidden rounded-lg">
  <Video src="connections.mp4" width={700} height={450} />
</div>

## Core Block Types

Sim provides seven core block types that handle the essential functions of AI workflows:

### Processing Blocks
- **[Agent](/blocks/agent)** - Chat with AI models (OpenAI, Anthropic, Google, local models)
- **[Function](/blocks/function)** - Run custom JavaScript/TypeScript code
- **[API](/blocks/api)** - Connect to external services via HTTP requests

### Logic Blocks
- **[Condition](/blocks/condition)** - Branch workflow paths based on boolean expressions
- **[Router](/blocks/router)** - Use AI to intelligently route requests to different paths
- **[Evaluator](/blocks/evaluator)** - Score and assess content quality using AI

### Output Blocks
- **[Response](/blocks/response)** - Format and return final results from your workflow

## How Blocks Work

Each block has three main components:

**Inputs**: Data coming into the block from other blocks or user input
**Configuration**: Settings that control how the block behaves
**Outputs**: Data the block produces for other blocks to use

<Steps>
  <Step>
    <strong>Receive Input</strong>: Block receives data from connected blocks or user input
  </Step>
  <Step>
    <strong>Process</strong>: Block processes the input according to its configuration
  </Step>
  <Step>
    <strong>Output Results</strong>: Block produces output data for the next blocks in the workflow
  </Step>
</Steps>

## Connecting Blocks

You create workflows by connecting blocks together. The output of one block becomes the input of another:

- **Drag to connect**: Drag from an output port to an input port
- **Multiple connections**: One output can connect to multiple inputs
- **Branching paths**: Some blocks can route to different paths based on conditions

<div className="w-full max-w-2xl mx-auto overflow-hidden rounded-lg">
  <Video src="connections.mp4" width={700} height={450} />
</div>

## Common Patterns

### Sequential Processing
Connect blocks in a chain where each block processes the output of the previous one:
```
User Input → Agent → Function → Response
```

### Conditional Branching
Use Condition or Router blocks to create different paths:
```
User Input → Router → Agent A (for questions)
                   → Agent B (for commands)
```

### Quality Control
Use Evaluator blocks to assess and filter outputs:
```
Agent → Evaluator → Condition → Response (if good)
                              → Agent (retry if bad)
```

## Block Configuration

Each block type has specific configuration options:

**All Blocks**:
- Input/output connections
- Error handling behavior
- Execution timeout settings

**AI Blocks** (Agent, Router, Evaluator):
- Model selection (OpenAI, Anthropic, Google, local)
- API keys and authentication
- Temperature and other model parameters
- System prompts and instructions

**Logic Blocks** (Condition, Function):
- Custom expressions or code
- Variable references
- Execution environment settings

**Integration Blocks** (API, Response):
- Endpoint configuration
- Headers and authentication
- Request/response formatting

<Cards>
  <Card title="Agent Block" href="/blocks/agent">
    Connect to AI models and create intelligent responses
  </Card>
  <Card title="Function Block" href="/blocks/function">
    Run custom code to process and transform data
  </Card>
  <Card title="API Block" href="/blocks/api">
    Integrate with external services and APIs
  </Card>
  <Card title="Condition Block" href="/blocks/condition">
    Create branching logic based on data evaluation
  </Card>
</Cards>
