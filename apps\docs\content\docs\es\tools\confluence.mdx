---
title: Confluence
description: Interactúa con Confluence
---

import { BlockInfoCard } from "@/components/ui/block-info-card"

<BlockInfoCard 
  type="confluence"
  color="#E0E0E0"
  icon={true}
  iconSvg={`<svg className="block-icon"
      
      
      
      viewBox='0 3 21 24'
      focusable='false'
      fill='none'
      aria-hidden='true'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        fill='#1868DB'
        d='M20.602 20.234c-6.584-3.183-8.507-3.66-11.281-3.66-3.255 0-6.03 1.355-8.507 5.16l-.407.622c-.333.513-.407.696-.407.915s.111.403.518.659l4.18 2.598c.221.146.406.22.591.22.222 0 .37-.11.592-.44l.666-1.024c1.035-1.574 1.96-2.086 3.144-2.086 1.035 0 2.256.293 3.772 1.025l4.365 2.049c.444.22.925.11 1.146-.403l2.072-4.537c.222-.512.074-.842-.444-1.098M1.406 12.22c6.583 3.184 8.507 3.66 11.28 3.66 3.256 0 6.03-1.354 8.508-5.16l.407-.622c.332-.512.406-.695.406-.915s-.11-.402-.518-.658L17.31 5.927c-.222-.147-.407-.22-.592-.22-.222 0-.37.11-.592.44l-.665 1.024c-1.036 1.573-1.96 2.086-3.144 2.086-1.036 0-2.257-.293-3.773-1.025L4.18 6.183c-.444-.22-.925-.11-1.147.402L.962 11.123c-.222.512-.074.841.444 1.098'
      />
    </svg>`}
/>

{/* MANUAL-CONTENT-START:intro */}
[Confluence](https://www.atlassian.com/software/confluence) es la potente plataforma de colaboración en equipo y gestión del conocimiento de Atlassian. Sirve como un espacio de trabajo centralizado donde los equipos pueden crear, organizar y compartir información entre departamentos y organizaciones.

Con Confluence, puedes:

- **Crear documentación estructurada**: Construir wikis completas, planes de proyectos y bases de conocimiento con formato enriquecido
- **Colaborar en tiempo real**: Trabajar juntos en documentos con compañeros de equipo, con comentarios, menciones y capacidades de edición
- **Organizar la información jerárquicamente**: Estructurar el contenido con espacios, páginas y jerarquías anidadas para una navegación intuitiva
- **Integrar con otras herramientas**: Conectar con Jira, Trello y otros productos de Atlassian para una integración fluida del flujo de trabajo
- **Controlar permisos de acceso**: Gestionar quién puede ver, editar o comentar contenido específico

En Sim, la integración con Confluence permite a tus agentes acceder y aprovechar la base de conocimientos de tu organización. Los agentes pueden recuperar información de las páginas de Confluence, buscar contenido específico e incluso actualizar la documentación cuando sea necesario. Esto permite que tus flujos de trabajo incorporen el conocimiento colectivo almacenado en tu instancia de Confluence, haciendo posible crear agentes que puedan consultar documentación interna, seguir procedimientos establecidos y mantener recursos de información actualizados como parte de sus operaciones.
{/* MANUAL-CONTENT-END */}

## Instrucciones de uso

Conéctate a espacios de trabajo de Confluence para recuperar y buscar documentación. Accede al contenido de las páginas, metadatos e integra la documentación de Confluence en tus flujos de trabajo.

## Herramientas

### `confluence_retrieve`

Recupera contenido de las páginas de Confluence utilizando la API de Confluence.

#### Entrada

| Parámetro | Tipo | Obligatorio | Descripción |
| --------- | ---- | -------- | ----------- |
| `domain` | string | Sí | Tu dominio de Confluence \(p. ej., tuempresa.atlassian.net\) |
| `pageId` | string | Sí | ID de la página de Confluence a recuperar |
| `cloudId` | string | No | ID de Confluence Cloud para la instancia. Si no se proporciona, se obtendrá utilizando el dominio. |

#### Salida

| Parámetro | Tipo | Descripción |
| --------- | ---- | ----------- |
| `ts` | string | Marca de tiempo de la recuperación |
| `pageId` | string | ID de la página de Confluence |
| `content` | string | Contenido de la página con etiquetas HTML eliminadas |
| `title` | string | Título de la página |

### `confluence_update`

Actualiza una página de Confluence utilizando la API de Confluence.

#### Entrada

| Parámetro | Tipo | Obligatorio | Descripción |
| --------- | ---- | -------- | ----------- |
| `domain` | string | Sí | Tu dominio de Confluence \(p. ej., tuempresa.atlassian.net\) |
| `pageId` | string | Sí | ID de la página de Confluence a actualizar |
| `title` | string | No | Nuevo título para la página |
| `content` | string | No | Nuevo contenido para la página en formato de almacenamiento de Confluence |
| `version` | number | No | Número de versión de la página \(requerido para prevenir conflictos\) |
| `cloudId` | string | No | ID de Confluence Cloud para la instancia. Si no se proporciona, se obtendrá utilizando el dominio. |

#### Salida

| Parámetro | Tipo | Descripción |
| --------- | ---- | ----------- |
| `ts` | string | Marca de tiempo de actualización |
| `pageId` | string | ID de página de Confluence |
| `title` | string | Título de página actualizado |
| `success` | boolean | Estado de éxito de la operación de actualización |

## Notas

- Categoría: `tools`
- Tipo: `confluence`
