---
title: Qdrant
description: Use Qdrant vector database
---

import { BlockInfoCard } from "@/components/ui/block-info-card"

<BlockInfoCard 
  type="qdrant"
  color="#1A223F"
  icon={true}
  iconSvg={`<svg className="block-icon"  fill='none' viewBox='0 0 49 56' xmlns='http://www.w3.org/2000/svg'>
      <g clipPath='url(#b)'>
        <path
          d='m38.489 51.477-1.1167-30.787-2.0223-8.1167 13.498 1.429v37.242l-8.2456 4.7589-2.1138-4.5259z'
          clipRule='evenodd'
          fill='#24386C'
          fillRule='evenodd'
        />
        <path
          d='m48.847 14-8.2457 4.7622-17.016-3.7326-19.917 8.1094-3.3183-9.139 12.122-7 12.126-7 12.123 7 12.126 7z'
          clipRule='evenodd'
          fill='#7589BE'
          fillRule='evenodd'
        />
        <path
          d='m0.34961 13.999 8.2457 4.7622 4.7798 14.215 16.139 12.913-4.9158 10.109-12.126-7.0004-12.123-7v-28z'
          clipRule='evenodd'
          fill='#B2BFE8'
          fillRule='evenodd'
        />
        <path
          d='m30.066 38.421-5.4666 8.059v9.5207l7.757-4.4756 3.9968-5.9681'
          clipRule='evenodd'
          fill='#24386C'
          fillRule='evenodd'
        />
        <path
          d='m24.602 36.962-7.7603-13.436 1.6715-4.4531 6.3544-3.0809 7.488 7.5343-7.7536 13.436z'
          clipRule='evenodd'
          fill='#7589BE'
          fillRule='evenodd'
        />
        <path
          d='m16.843 23.525 7.7569 4.4756v8.9585l-7.1741 0.3087-4.3397-5.5412 3.7569-8.2016z'
          clipRule='evenodd'
          fill='#B2BFE8'
          fillRule='evenodd'
        />
        <path
          d='m24.6 28 7.757-4.4752 5.2792 8.7903-6.3886 5.2784-6.6476-0.6346v-8.9589z'
          clipRule='evenodd'
          fill='#24386C'
          fillRule='evenodd'
        />
        <path
          d='m32.355 51.524 8.2457 4.476v-37.238l-8.0032-4.6189-7.9995-4.6189-8.0031 4.6189-7.9995 4.6189v18.479l7.9995 4.6189 8.0031 4.6193 7.757-4.4797v9.5244zm0-19.045-7.757 4.4793-7.7569-4.4793v-8.9549l7.7569-4.4792 7.757 4.4792v8.9549z'
          clipRule='evenodd'
          fill='#DC244C'
          fillRule='evenodd'
        />
        <path d='m24.603 46.483v-9.5222l-7.7166-4.4411v9.5064l7.7166 4.4569z' fill='url(#a)' />
      </g>
      <defs>
        <linearGradient
          id='a'
          x1='23.18'
          x2='15.491'
          y1='38.781'
          y2='38.781'
          gradientUnits='userSpaceOnUse'
        >
          <stop stopColor='#FF3364' offset='0' />
          <stop stopColor='#C91540' stopOpacity='0' offset='1' />
        </linearGradient>
        <clipPath id='b'>
          <rect transform='translate(.34961)'   fill='#fff' />
        </clipPath>
      </defs>
    </svg>`}
/>

{/* MANUAL-CONTENT-START:intro */}
[Qdrant](https://qdrant.tech) is an open-source vector database designed for efficient storage, management, and retrieval of high-dimensional vector embeddings. Qdrant enables fast and scalable semantic search, making it ideal for AI applications that require similarity search, recommendation systems, and contextual information retrieval.

With Qdrant, you can:

- **Store vector embeddings**: Efficiently manage and persist high-dimensional vectors at scale
- **Perform semantic similarity search**: Find the most similar vectors to a query vector in real time
- **Filter and organize data**: Use advanced filtering to narrow down search results based on metadata or payload
- **Fetch specific points**: Retrieve vectors and their associated payloads by ID
- **Scale seamlessly**: Handle large collections and high-throughput workloads

In Sim, the Qdrant integration enables your agents to interact with Qdrant programmatically as part of their workflows. Supported operations include:

- **Upsert**: Insert or update points (vectors and payloads) in a Qdrant collection
- **Search**: Perform similarity search to find vectors most similar to a given query vector, with optional filtering and result customization
- **Fetch**: Retrieve specific points from a collection by their IDs, with options to include payloads and vectors

This integration allows your agents to leverage powerful vector search and management capabilities, enabling advanced automation scenarios such as semantic search, recommendation, and contextual retrieval. By connecting Sim with Qdrant, you can build agents that understand context, retrieve relevant information from large datasets, and deliver more intelligent and personalized responses—all without managing complex infrastructure.
{/* MANUAL-CONTENT-END */}


## Usage Instructions

Store, search, and retrieve vector embeddings using Qdrant. Perform semantic similarity searches and manage your vector collections.



## Tools

### `qdrant_upsert_points`

Insert or update points in a Qdrant collection

#### Input

| Parameter | Type | Required | Description |
| --------- | ---- | -------- | ----------- |
| `url` | string | Yes | Qdrant base URL |
| `apiKey` | string | No | Qdrant API key \(optional\) |
| `collection` | string | Yes | Collection name |
| `points` | array | Yes | Array of points to upsert |

#### Output

| Parameter | Type | Description |
| --------- | ---- | ----------- |
| `status` | string | Status of the upsert operation |
| `data` | object | Result data from the upsert operation |

### `qdrant_search_vector`

Search for similar vectors in a Qdrant collection

#### Input

| Parameter | Type | Required | Description |
| --------- | ---- | -------- | ----------- |
| `url` | string | Yes | Qdrant base URL |
| `apiKey` | string | No | Qdrant API key \(optional\) |
| `collection` | string | Yes | Collection name |
| `vector` | array | Yes | Vector to search for |
| `limit` | number | No | Number of results to return |
| `filter` | object | No | Filter to apply to the search |
| `with_payload` | boolean | No | Include payload in response |
| `with_vector` | boolean | No | Include vector in response |

#### Output

| Parameter | Type | Description |
| --------- | ---- | ----------- |
| `data` | array | Vector search results with ID, score, payload, and optional vector data |
| `status` | string | Status of the search operation |

### `qdrant_fetch_points`

Fetch points by ID from a Qdrant collection

#### Input

| Parameter | Type | Required | Description |
| --------- | ---- | -------- | ----------- |
| `url` | string | Yes | Qdrant base URL |
| `apiKey` | string | No | Qdrant API key \(optional\) |
| `collection` | string | Yes | Collection name |
| `ids` | array | Yes | Array of point IDs to fetch |
| `with_payload` | boolean | No | Include payload in response |
| `with_vector` | boolean | No | Include vector in response |

#### Output

| Parameter | Type | Description |
| --------- | ---- | ----------- |
| `data` | array | Fetched points with ID, payload, and optional vector data |
| `status` | string | Status of the fetch operation |



## Notes

- Category: `tools`
- Type: `qdrant`
