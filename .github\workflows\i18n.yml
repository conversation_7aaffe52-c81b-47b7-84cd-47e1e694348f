name: 'Auto-translate Documentation'

on:
  push:
    branches: [ main ]
    paths: 
      - 'apps/docs/content/docs/en/**'
      - 'apps/docs/i18n.json'
  pull_request:
    branches: [ main ]
    paths: 
      - 'apps/docs/content/docs/en/**'
      - 'apps/docs/i18n.json'
  workflow_dispatch: # Allow manual triggers

jobs:
  translate:
    runs-on: ubuntu-latest
    if: github.actor != 'github-actions[bot]' # Prevent infinite loops
    
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          fetch-depth: 0
      
      - name: Setup Bun
        uses: oven-sh/setup-bun@v1
        with:
          bun-version: latest
      
      - name: Run Lingo.dev translations
        env:
          LINGODOTDEV_API_KEY: ${{ secrets.LINGODOTDEV_API_KEY }}
        run: |
          cd apps/docs
          bunx lingo.dev@latest i18n
      
      - name: Check for translation changes
        id: changes
        run: |
          cd apps/docs
          git config --local user.email "<EMAIL>"
          git config --local user.name "GitHub Action"
          
          if [ -n "$(git status --porcelain content/docs)" ]; then
            echo "changes=true" >> $GITHUB_OUTPUT
          else
            echo "changes=false" >> $GITHUB_OUTPUT
          fi
      
      - name: Commit and push translation updates
        if: steps.changes.outputs.changes == 'true'
        run: |
          cd apps/docs
          git add content/docs/es/ content/docs/fr/ content/docs/zh/ i18n.lock
          git commit -m "feat: update translations"
          git push origin ${{ github.ref_name }}
      
      - name: Create Pull Request (for feature branches)
        if: steps.changes.outputs.changes == 'true' && github.event_name == 'pull_request'
        uses: peter-evans/create-pull-request@v5
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          commit-message: "feat: update translations"
          title: "🌐 Auto-update translations"
          body: |
            ## Summary
            Automated translation updates for documentation.
            
            - Updated translations for modified English content
            - Generated using Lingo.dev AI translation
            - Maintains consistency with source documentation
            
            ## Test Plan
            - [ ] Verify translated content accuracy
            - [ ] Check that all links and references work correctly
            - [ ] Ensure formatting and structure are preserved
          branch: auto-translations
          base: ${{ github.base_ref }}
          labels: |
            i18n
            auto-generated

  verify-translations:
    needs: translate
    runs-on: ubuntu-latest
    if: always() # Run even if translation fails
    
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
      
      - name: Setup Bun
        uses: oven-sh/setup-bun@v1
        with:
          bun-version: latest
      
      - name: Install dependencies
        run: |
          cd apps/docs
          bun install
      
      - name: Build documentation to verify translations
        run: |
          cd apps/docs
          bun run build
      
      - name: Report translation status
        run: |
          cd apps/docs
          echo "## Translation Status Report" >> $GITHUB_STEP_SUMMARY
          
          en_count=$(find content/docs/en -name "*.mdx" | wc -l)
          es_count=$(find content/docs/es -name "*.mdx" 2>/dev/null | wc -l || echo 0)
          fr_count=$(find content/docs/fr -name "*.mdx" 2>/dev/null | wc -l || echo 0)
          zh_count=$(find content/docs/zh -name "*.mdx" 2>/dev/null | wc -l || echo 0)
          
          es_percentage=$((es_count * 100 / en_count))
          fr_percentage=$((fr_count * 100 / en_count))
          zh_percentage=$((zh_count * 100 / en_count))
          
          echo "- **🇪🇸 Spanish**: $es_count/$en_count files ($es_percentage%)" >> $GITHUB_STEP_SUMMARY
          echo "- **🇫🇷 French**: $fr_count/$en_count files ($fr_percentage%)" >> $GITHUB_STEP_SUMMARY
          echo "- **🇨🇳 Chinese**: $zh_count/$en_count files ($zh_percentage%)" >> $GITHUB_STEP_SUMMARY