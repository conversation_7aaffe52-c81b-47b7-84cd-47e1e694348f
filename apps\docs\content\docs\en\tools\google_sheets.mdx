---
title: Google Sheets
description: Read, write, and update data
---

import { BlockInfoCard } from "@/components/ui/block-info-card"

<BlockInfoCard 
  type="google_sheets"
  color="#E0E0E0"
  icon={true}
  iconSvg={`<svg className="block-icon"
      
      xmlns='http://www.w3.org/2000/svg'
      viewBox='0 0 48 48'
      
      
    >
      <path
        fill='#43a047'
        d='M37,45H11c-1.657,0-3-1.343-3-3V6c0-1.657,1.343-3,3-3h19l10,10v29C40,43.657,38.657,45,37,45z'
      />
      <path fill='#c8e6c9' d='M40 13L30 13 30 3z' />
      <path fill='#2e7d32' d='M30 13L40 23 40 13z' />
      <path
        fill='#e8f5e9'
        d='M31,23H17h-2v2v2v2v2v2v2v2h18v-2v-2v-2v-2v-2v-2v-2H31z M17,25h4v2h-4V25z M17,29h4v2h-4V29z M17,33h4v2h-4V33z M31,35h-8v-2h8V35z M31,31h-8v-2h8V31z M31,27h-8v-2h8V27z'
      />
    </svg>`}
/>

{/* MANUAL-CONTENT-START:intro */}
[Google Sheets](https://sheets.google.com) is a powerful cloud-based spreadsheet application that allows users to create, edit, and collaborate on spreadsheets in real-time. As part of Google's productivity suite, Google Sheets offers a versatile platform for data organization, analysis, and visualization with robust formatting, formula, and sharing capabilities.

Learn how to integrate the Google Sheets "Read" tool in Sim to effortlessly fetch data from your spreadsheets to integrate into your workflows. This tutorial walks you through connecting Google Sheets, setting up data reads, and using that information to automate processes in real-time. Perfect for syncing live data with your agents.

<iframe
  width="100%"
  height="400"
  src="https://www.youtube.com/embed/xxP7MZRuq_0"
  title="Use the Google Sheets Read tool in Sim"
  frameBorder="0"
  allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
  allowFullScreen
></iframe>

Discover how to use the Google Sheets "Write" tool in Sim to automatically send data from your workflows to your Google Sheets. This tutorial covers setting up the integration, configuring write operations, and updating your sheets seamlessly as workflows execute. Perfect for maintaining real-time records without manual input.

<iframe
  width="100%"
  height="400"
  src="https://www.youtube.com/embed/cO86qTj7qeY"
  title="Use the Google Sheets Write tool in Sim"
  frameBorder="0"
  allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
  allowFullScreen
></iframe>

Explore how to leverage the Google Sheets "Update" tool in Sim to modify existing entries in your spreadsheets based on workflow execution. This tutorial demonstrates setting up the update logic, mapping data fields, and synchronizing changes instantly. Perfect for keeping your data current and consistent.

<iframe
  width="100%"
  height="400"
  src="https://www.youtube.com/embed/95by2fL9yn4"
  title="Use the Google Sheets Update tool in Sim"
  frameBorder="0"
  allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
  allowFullScreen
></iframe>

Learn how to use the Google Sheets "Append" tool in Sim to effortlessly add new rows of data to your spreadsheets during workflow execution. This tutorial walks you through setting up the integration, configuring append actions, and ensuring smooth data growth. Perfect for expanding records without manual effort!

<iframe
  width="100%"
  height="400"
  src="https://www.youtube.com/embed/8DgNvLBCsAo"
  title="Use the Google Sheets Append tool in Sim"
  frameBorder="0"
  allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
  allowFullScreen
></iframe>

With Google Sheets, you can:

- **Create and edit spreadsheets**: Develop data-driven documents with comprehensive formatting and calculation options
- **Collaborate in real-time**: Work simultaneously with multiple users on the same spreadsheet
- **Analyze data**: Use formulas, functions, and pivot tables to process and understand your data
- **Visualize information**: Create charts, graphs, and conditional formatting to represent data visually
- **Access anywhere**: Use Google Sheets across devices with automatic cloud synchronization
- **Work offline**: Continue working without internet connection with changes syncing when back online
- **Integrate with other services**: Connect with Google Drive, Forms, and third-party applications

In Sim, the Google Sheets integration enables your agents to interact directly with spreadsheet data programmatically. This allows for powerful automation scenarios such as data extraction, analysis, reporting, and management. Your agents can read existing spreadsheets to extract information, write to spreadsheets to update data, and create new spreadsheets from scratch. This integration bridges the gap between your AI workflows and data management, enabling seamless interaction with structured data. By connecting Sim with Google Sheets, you can automate data workflows, generate reports, extract insights from data, and maintain up-to-date information - all through your intelligent agents. The integration supports various data formats and range specifications, making it flexible enough to handle diverse data management needs while maintaining the collaborative and accessible nature of Google Sheets.
{/* MANUAL-CONTENT-END */}


## Usage Instructions

Integrate Google Sheets functionality to manage spreadsheet data. Read data from specific ranges, write new data, update existing cells, and append data to the end of sheets using OAuth authentication. Supports various input and output formats for flexible data handling.



## Tools

### `google_sheets_read`

Read data from a Google Sheets spreadsheet

#### Input

| Parameter | Type | Required | Description |
| --------- | ---- | -------- | ----------- |
| `spreadsheetId` | string | Yes | The ID of the spreadsheet to read from |
| `range` | string | No | The range of cells to read from |

#### Output

| Parameter | Type | Description |
| --------- | ---- | ----------- |
| `data` | json | Sheet data including range and cell values |
| `metadata` | json | Spreadsheet metadata including ID and URL |

### `google_sheets_write`

Write data to a Google Sheets spreadsheet

#### Input

| Parameter | Type | Required | Description |
| --------- | ---- | -------- | ----------- |
| `spreadsheetId` | string | Yes | The ID of the spreadsheet to write to |
| `range` | string | No | The range of cells to write to |
| `values` | array | Yes | The data to write to the spreadsheet |
| `valueInputOption` | string | No | The format of the data to write |
| `includeValuesInResponse` | boolean | No | Whether to include the written values in the response |

#### Output

| Parameter | Type | Description |
| --------- | ---- | ----------- |
| `updatedRange` | string | Range of cells that were updated |
| `updatedRows` | number | Number of rows updated |
| `updatedColumns` | number | Number of columns updated |
| `updatedCells` | number | Number of cells updated |
| `metadata` | json | Spreadsheet metadata including ID and URL |

### `google_sheets_update`

Update data in a Google Sheets spreadsheet

#### Input

| Parameter | Type | Required | Description |
| --------- | ---- | -------- | ----------- |
| `spreadsheetId` | string | Yes | The ID of the spreadsheet to update |
| `range` | string | No | The range of cells to update |
| `values` | array | Yes | The data to update in the spreadsheet |
| `valueInputOption` | string | No | The format of the data to update |
| `includeValuesInResponse` | boolean | No | Whether to include the updated values in the response |

#### Output

| Parameter | Type | Description |
| --------- | ---- | ----------- |
| `updatedRange` | string | Range of cells that were updated |
| `updatedRows` | number | Number of rows updated |
| `updatedColumns` | number | Number of columns updated |
| `updatedCells` | number | Number of cells updated |
| `metadata` | json | Spreadsheet metadata including ID and URL |

### `google_sheets_append`

Append data to the end of a Google Sheets spreadsheet

#### Input

| Parameter | Type | Required | Description |
| --------- | ---- | -------- | ----------- |
| `spreadsheetId` | string | Yes | The ID of the spreadsheet to append to |
| `range` | string | No | The range of cells to append after |
| `values` | array | Yes | The data to append to the spreadsheet |
| `valueInputOption` | string | No | The format of the data to append |
| `insertDataOption` | string | No | How to insert the data \(OVERWRITE or INSERT_ROWS\) |
| `includeValuesInResponse` | boolean | No | Whether to include the appended values in the response |

#### Output

| Parameter | Type | Description |
| --------- | ---- | ----------- |
| `tableRange` | string | Range of the table where data was appended |
| `updatedRange` | string | Range of cells that were updated |
| `updatedRows` | number | Number of rows updated |
| `updatedColumns` | number | Number of columns updated |
| `updatedCells` | number | Number of cells updated |
| `metadata` | json | Spreadsheet metadata including ID and URL |



## Notes

- Category: `tools`
- Type: `google_sheets`
