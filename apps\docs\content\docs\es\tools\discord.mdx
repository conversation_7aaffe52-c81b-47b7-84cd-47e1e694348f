---
title: Discord
description: Interactúa con Discord
---

import { BlockInfoCard } from "@/components/ui/block-info-card"

<BlockInfoCard 
  type="discord"
  color="#E0E0E0"
  icon={true}
  iconSvg={`<svg className="block-icon"
      
      
      
      viewBox='0 -28.5 256 256'
      version='1.1'
      xmlns='http://www.w3.org/2000/svg'
      xmlnsXlink='http://www.w3.org/1999/xlink'
      preserveAspectRatio='xMidYMid'
    >
      <g>
        <path
          d='M216.856339,16.5966031 C200.285002,8.84328665 182.566144,3.2084988 164.041564,0 C161.766523,4.11318106 159.108624,9.64549908 157.276099,14.0464379 C137.583995,11.0849896 118.072967,11.0849896 98.7430163,14.0464379 C96.9108417,9.64549908 94.1925838,4.11318106 91.8971895,0 C73.3526068,3.2084988 55.6133949,8.86399117 39.0420583,16.6376612 C5.61752293,67.146514 -3.4433191,116.400813 1.08711069,164.955721 C23.2560196,181.510915 44.7403634,191.567697 65.8621325,198.148576 C71.0772151,190.971126 75.7283628,183.341335 79.7352139,175.300261 C72.104019,172.400575 64.7949724,168.822202 57.8887866,164.667963 C59.7209612,163.310589 61.5131304,161.891452 63.2445898,160.431257 C105.36741,180.133187 151.134928,180.133187 192.754523,160.431257 C194.506336,161.891452 196.298154,163.310589 198.110326,164.667963 C191.183787,168.842556 183.854737,172.420929 176.223542,175.320965 C180.230393,183.341335 184.861538,190.991831 190.096624,198.16893 C211.238746,191.588051 232.743023,181.531619 254.911949,164.955721 C260.227747,108.668201 245.831087,59.8662432 216.856339,16.5966031 Z M85.4738752,135.09489 C72.8290281,135.09489 62.4592217,123.290155 62.4592217,108.914901 C62.4592217,94.5396472 72.607595,82.7145587 85.4738752,82.7145587 C98.3405064,82.7145587 108.709962,94.5189427 108.488529,108.914901 C108.508531,123.290155 98.3405064,135.09489 85.4738752,135.09489 Z M170.525237,135.09489 C157.88039,135.09489 147.510584,123.290155 147.510584,108.914901 C147.510584,94.5396472 157.658606,82.7145587 170.525237,82.7145587 C183.391518,82.7145587 193.761324,94.5189427 193.539891,108.914901 C193.539891,123.290155 183.391518,135.09489 170.525237,135.09489 Z'
          fill='#5865F2'
          fillRule='nonzero'
        />
      </g>
    </svg>`}
/>

{/* MANUAL-CONTENT-START:intro */}
[Discord](https://discord.com) es una potente plataforma de comunicación que te permite conectar con amigos, comunidades y equipos. Ofrece una variedad de funciones para la colaboración en equipo, incluyendo canales de texto, canales de voz y videollamadas.

Con una cuenta o bot de Discord, puedes:

- **Enviar mensajes**: Enviar mensajes a un canal específico
- **Obtener mensajes**: Obtener mensajes de un canal específico
- **Obtener servidor**: Obtener información sobre un servidor específico
- **Obtener usuario**: Obtener información sobre un usuario específico

En Sim, la integración con Discord permite a tus agentes acceder y aprovechar los servidores de Discord de tu organización. Los agentes pueden recuperar información de los canales de Discord, buscar usuarios específicos, obtener información del servidor y enviar mensajes. Esto permite que tus flujos de trabajo se integren con tus comunidades de Discord, automaticen notificaciones y creen experiencias interactivas.

> **Importante:** Para leer el contenido de los mensajes, tu bot de Discord necesita tener habilitado el "Message Content Intent" en el Portal de Desarrolladores de Discord. Sin este permiso, seguirás recibiendo los metadatos del mensaje pero el campo de contenido aparecerá vacío.

Los componentes de Discord en Sim utilizan una carga diferida eficiente, obteniendo datos solo cuando es necesario para minimizar las llamadas a la API y evitar limitaciones de tasa. La actualización de tokens ocurre automáticamente en segundo plano para mantener tu conexión.

### Configuración de tu bot de Discord

1. Ve al [Portal de Desarrolladores de Discord](https://discord.com/developers/applications)
2. Crea una nueva aplicación y navega a la pestaña "Bot"
3. Crea un bot y copia tu token de bot
4. En "Privileged Gateway Intents", habilita el **Message Content Intent** para leer el contenido de los mensajes
5. Invita a tu bot a tus servidores con los permisos apropiados
{/* MANUAL-CONTENT-END */}

## Instrucciones de uso

Conéctate a Discord para enviar mensajes, gestionar canales e interactuar con servidores. Automatiza notificaciones, gestión de comunidad e integra Discord en tus flujos de trabajo.

## Herramientas

### `discord_send_message`

Enviar un mensaje a un canal de Discord

#### Entrada

| Parámetro | Tipo | Obligatorio | Descripción |
| --------- | ---- | ----------- | ----------- |
| `botToken` | string | Sí | El token del bot para autenticación |
| `channelId` | string | Sí | El ID del canal de Discord donde enviar el mensaje |
| `content` | string | No | El contenido de texto del mensaje |
| `serverId` | string | Sí | El ID del servidor de Discord \(ID del guild\) |

#### Salida

| Parámetro | Tipo | Descripción |
| --------- | ---- | ----------- |
| `message` | string | Mensaje de éxito o error |
| `data` | object | Datos del mensaje de Discord |

### `discord_get_messages`

Recuperar mensajes de un canal de Discord

#### Entrada

| Parámetro | Tipo | Obligatorio | Descripción |
| --------- | ---- | -------- | ----------- |
| `botToken` | string | Sí | El token del bot para autenticación |
| `channelId` | string | Sí | El ID del canal de Discord del que recuperar mensajes |
| `limit` | number | No | Número máximo de mensajes a recuperar \(predeterminado: 10, máx: 100\) |

#### Salida

| Parámetro | Tipo | Descripción |
| --------- | ---- | ----------- |
| `message` | string | Mensaje de éxito o error |
| `messages` | array | Array de mensajes de Discord con metadatos completos |

### `discord_get_server`

Recuperar información sobre un servidor de Discord (guild)

#### Entrada

| Parámetro | Tipo | Obligatorio | Descripción |
| --------- | ---- | -------- | ----------- |
| `botToken` | string | Sí | El token del bot para autenticación |
| `serverId` | string | Sí | El ID del servidor de Discord \(ID del guild\) |

#### Salida

| Parámetro | Tipo | Descripción |
| --------- | ---- | ----------- |
| `message` | string | Mensaje de éxito o error |
| `data` | object | Información del servidor de Discord \(guild\) |

### `discord_get_user`

Recuperar información sobre un usuario de Discord

#### Entrada

| Parámetro | Tipo | Obligatorio | Descripción |
| --------- | ---- | -------- | ----------- |
| `botToken` | string | Sí | Token del bot de Discord para autenticación |
| `userId` | string | Sí | El ID del usuario de Discord |

#### Salida

| Parámetro | Tipo | Descripción |
| --------- | ---- | ----------- |
| `message` | string | Mensaje de éxito o error |
| `data` | object | Información del usuario de Discord |

## Notas

- Categoría: `tools`
- Tipo: `discord`
