---
title: OneDrive
description: <PERSON>rea<PERSON>, subir y listar archivos
---

import { BlockInfoCard } from "@/components/ui/block-info-card"

<BlockInfoCard 
  type="onedrive"
  color="#E0E0E0"
  icon={true}
  iconSvg={`<svg className="block-icon"  fill='currentColor' viewBox='0 0 32 32' xmlns='http://www.w3.org/2000/svg'>
      <g>
        <path
          d='M12.20245,11.19292l.00031-.0011,6.71765,4.02379,4.00293-1.68451.00018.00068A6.4768,6.4768,0,0,1,25.5,13c.14764,0,.29358.0067.43878.01639a10.00075,10.00075,0,0,0-18.041-3.01381C7.932,10.00215,7.9657,10,8,10A7.96073,7.96073,0,0,1,12.20245,11.19292Z'
          fill='#0364b8'
        />
        <path
          d='M12.20276,11.19182l-.00031.0011A7.96073,7.96073,0,0,0,8,10c-.0343,0-.06805.00215-.10223.00258A7.99676,7.99676,0,0,0,1.43732,22.57277l5.924-2.49292,2.63342-1.10819,5.86353-2.46746,3.06213-1.28859Z'
          fill='#0078d4'
        />
        <path
          d='M25.93878,13.01639C25.79358,13.0067,25.64764,13,25.5,13a6.4768,6.4768,0,0,0-2.57648.53178l-.00018-.00068-4.00293,1.68451,1.16077.69528L23.88611,18.19l1.66009.99438,5.67633,3.40007a6.5002,6.5002,0,0,0-5.28375-9.56805Z'
          fill='#1490df'
        />
        <path
          d='M25.5462,19.18437,23.88611,18.19l-3.80493-2.2791-1.16077-.69528L15.85828,16.5042,9.99475,18.97166,7.36133,20.07985l-5.924,2.49292A7.98889,7.98889,0,0,0,8,26H25.5a6.49837,6.49837,0,0,0,5.72253-3.41556Z'
          fill='#28a8ea'
        />
      </g>
    </svg>`}
/>

{/* MANUAL-CONTENT-START:intro */}
[OneDrive](https://onedrive.live.com) es el servicio de almacenamiento en la nube y sincronización de archivos de Microsoft que permite a los usuarios almacenar, acceder y compartir archivos de forma segura en todos sus dispositivos. Integrado profundamente en el ecosistema de Microsoft 365, OneDrive facilita la colaboración fluida, el control de versiones y el acceso en tiempo real al contenido entre equipos y organizaciones.

Aprende a integrar la herramienta OneDrive en Sim para extraer, gestionar y organizar automáticamente tus archivos en la nube dentro de tus flujos de trabajo. Este tutorial te guía a través de la conexión con OneDrive, la configuración del acceso a archivos y el uso del contenido almacenado para impulsar la automatización. Ideal para sincronizar documentos y medios esenciales con tus agentes en tiempo real.

Con OneDrive, puedes:

- **Almacenar archivos de forma segura en la nube**: Subir y acceder a documentos, imágenes y otros archivos desde cualquier dispositivo
- **Organizar tu contenido**: Crear carpetas estructuradas y gestionar versiones de archivos con facilidad
- **Colaborar en tiempo real**: Compartir archivos, editarlos simultáneamente con otros y realizar seguimiento de cambios
- **Acceder desde múltiples dispositivos**: Usar OneDrive desde plataformas de escritorio, móviles y web
- **Integrar con Microsoft 365**: Trabajar sin problemas con Word, Excel, PowerPoint y Teams
- **Controlar permisos**: Compartir archivos y carpetas con configuraciones de acceso personalizadas y controles de caducidad

En Sim, la integración con OneDrive permite a tus agentes interactuar directamente con tu almacenamiento en la nube. Los agentes pueden subir nuevos archivos a carpetas específicas, recuperar y leer archivos existentes, y listar contenidos de carpetas para organizar y acceder a la información de forma dinámica. Esta integración permite a tus agentes incorporar operaciones con archivos en flujos de trabajo inteligentes — automatizando la recepción de documentos, el análisis de contenido y la gestión estructurada del almacenamiento. Al conectar Sim con OneDrive, capacitas a tus agentes para gestionar y utilizar documentos en la nube de forma programática, eliminando pasos manuales y mejorando la automatización con acceso seguro a archivos en tiempo real.
{/* MANUAL-CONTENT-END */}

## Instrucciones de uso

Integra la funcionalidad de OneDrive para gestionar archivos y carpetas. Sube nuevos archivos, crea nuevas carpetas y lista el contenido de las carpetas utilizando autenticación OAuth. Admite operaciones de archivos con tipos MIME personalizados y organización de carpetas.

## Herramientas

### `onedrive_upload`

Subir un archivo a OneDrive

#### Entrada

| Parámetro | Tipo | Obligatorio | Descripción |
| --------- | ---- | -------- | ----------- |
| `fileName` | string | Sí | El nombre del archivo a subir |
| `content` | string | Sí | El contenido del archivo a subir |
| `folderSelector` | string | No | Seleccionar la carpeta donde subir el archivo |
| `manualFolderId` | string | No | ID de carpeta introducido manualmente \(modo avanzado\) |

#### Salida

| Parámetro | Tipo | Descripción |
| --------- | ---- | ----------- |
| `success` | boolean | Indica si el archivo se subió correctamente |
| `file` | object | El objeto del archivo subido con metadatos que incluyen id, nombre, webViewLink, webContentLink y marcas de tiempo |

### `onedrive_create_folder`

Crear una nueva carpeta en OneDrive

#### Entrada

| Parámetro | Tipo | Obligatorio | Descripción |
| --------- | ---- | -------- | ----------- |
| `folderName` | string | Sí | Nombre de la carpeta a crear |
| `folderSelector` | string | No | Seleccionar la carpeta principal donde crear la carpeta |
| `manualFolderId` | string | No | ID de carpeta principal introducido manualmente \(modo avanzado\) |

#### Salida

| Parámetro | Tipo | Descripción |
| --------- | ---- | ----------- |
| `success` | boolean | Indica si la carpeta se creó correctamente |
| `file` | object | El objeto de la carpeta creada con metadatos que incluyen id, nombre, webViewLink y marcas de tiempo |

### `onedrive_list`

Listar archivos y carpetas en OneDrive

#### Entrada

| Parámetro | Tipo | Obligatorio | Descripción |
| --------- | ---- | ----------- | ----------- |
| `folderSelector` | string | No | Seleccionar la carpeta para listar archivos |
| `manualFolderId` | string | No | El ID de carpeta ingresado manualmente \(modo avanzado\) |
| `query` | string | No | Una consulta para filtrar los archivos |
| `pageSize` | number | No | El número de archivos a devolver |

#### Salida

| Parámetro | Tipo | Descripción |
| --------- | ---- | ----------- |
| `success` | boolean | Si los archivos se listaron correctamente |
| `files` | array | Array de objetos de archivos y carpetas con metadatos |
| `nextPageToken` | string | Token para recuperar la siguiente página de resultados \(opcional\) |

## Notas

- Categoría: `tools`
- Tipo: `onedrive`
