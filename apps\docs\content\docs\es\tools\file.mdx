---
title: Archivo
description: <PERSON>r y analizar múltiples archivos
---

import { BlockInfoCard } from "@/components/ui/block-info-card"

<BlockInfoCard 
  type="file"
  color="#40916C"
  icon={true}
  iconSvg={`<svg className="block-icon"
      
      
      
      viewBox='0 0 23 28'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        d='M8 15.2H15.2M8 20H11.6M2 4.4V23.6C2 24.2365 2.25286 24.847 2.70294 25.2971C3.15303 25.7471 3.76348 26 4.4 26H18.8C19.4365 26 20.047 25.7471 20.4971 25.2971C20.9471 24.847 21.2 24.2365 21.2 23.6V9.6104C21.2 9.29067 21.136 8.97417 21.012 8.67949C20.8879 8.38481 20.7062 8.11789 20.4776 7.8944L15.1496 2.684C14.7012 2.24559 14.0991 2.00008 13.472 2H4.4C3.76348 2 3.15303 2.25286 2.70294 2.70294C2.25286 3.15303 2 3.76348 2 4.4Z'
        stroke='currentColor'
        strokeWidth='2.25'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
      <path
        d='M14 2V6.8C14 7.43652 14.2529 8.04697 14.7029 8.49706C15.153 8.94714 15.7635 9.2 16.4 9.2H21.2'
        stroke='currentColor'
        strokeWidth='2.25'
        strokeLinejoin='round'
      />
    </svg>`}
/>

{/* MANUAL-CONTENT-START:intro */}
La herramienta de análisis de archivos proporciona una forma potente de extraer y procesar contenido de varios formatos de archivo, facilitando la incorporación de datos de documentos en los flujos de trabajo de tu agente. Esta herramienta es compatible con múltiples formatos de archivo y puede manejar archivos de hasta 200MB de tamaño.

Con el analizador de archivos, puedes:

- **Procesar múltiples formatos de archivo**: Extraer texto de PDFs, CSVs, documentos Word (DOCX), archivos de texto y más
- **Manejar archivos grandes**: Procesar documentos de hasta 200MB de tamaño
- **Analizar archivos desde URLs**: Extraer directamente contenido de archivos alojados en línea proporcionando sus URLs
- **Procesar múltiples archivos a la vez**: Subir y analizar varios archivos en una sola operación
- **Extraer datos estructurados**: Mantener el formato y la estructura de los documentos originales cuando sea posible

La herramienta de análisis de archivos es particularmente útil para escenarios donde tus agentes necesitan trabajar con contenido de documentos, como analizar informes, extraer datos de hojas de cálculo o procesar texto de varias fuentes de documentos. Simplifica el proceso de hacer que el contenido de documentos esté disponible para tus agentes, permitiéndoles trabajar con información almacenada en archivos tan fácilmente como con entrada de texto directa.
{/* MANUAL-CONTENT-END */}

## Instrucciones de uso

Sube y extrae contenido de formatos de archivos estructurados, incluyendo PDFs, hojas de cálculo CSV y documentos Word (DOCX). Puedes proporcionar una URL a un archivo o subir archivos directamente. Analizadores especializados extraen texto y metadatos de cada formato. Puedes subir múltiples archivos a la vez y acceder a ellos individualmente o como un documento combinado.

## Herramientas

### `file_parser`

Analiza uno o más archivos subidos o archivos desde URLs (texto, PDF, CSV, imágenes, etc.)

#### Entrada

| Parámetro | Tipo | Obligatorio | Descripción |
| --------- | ---- | ----------- | ----------- |
| `filePath` | string | Sí | Ruta al archivo\(s\). Puede ser una única ruta, URL o un array de rutas. |
| `fileType` | string | No | Tipo de archivo a analizar \(auto-detectado si no se especifica\) |

#### Salida

| Parámetro | Tipo | Descripción |
| --------- | ---- | ----------- |
| `files` | array | Array de archivos analizados |
| `combinedContent` | string | Contenido combinado de todos los archivos analizados |

## Notas

- Categoría: `tools`
- Tipo: `file`
