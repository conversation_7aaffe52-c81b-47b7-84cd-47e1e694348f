---
title: Slack
description: Env<PERSON> mensajes a Slack o activa flujos de trabajo desde eventos de Slack
---

import { BlockInfoCard } from "@/components/ui/block-info-card"

<BlockInfoCard 
  type="slack"
  color="#611f69"
  icon={true}
  iconSvg={`<svg className="block-icon" viewBox='0 0 256 256' xmlns='http://www.w3.org/2000/svg' >
      <g>
        <path
          d='M53.8412698,161.320635 C53.8412698,176.152381 41.8539683,188.139683 27.0222222,188.139683 C12.1904762,188.139683 0.203174603,176.152381 0.203174603,161.320635 C0.203174603,146.488889 12.1904762,134.501587 27.0222222,134.501587 L53.8412698,134.501587 L53.8412698,161.320635 Z M67.2507937,161.320635 C67.2507937,146.488889 79.2380952,134.501587 94.0698413,134.501587 C108.901587,134.501587 120.888889,146.488889 120.888889,161.320635 L120.888889,228.368254 C120.888889,243.2 108.901587,255.187302 94.0698413,255.187302 C79.2380952,255.187302 67.2507937,243.2 67.2507937,228.368254 L67.2507937,161.320635 Z'
          fill='#E01E5A'
        />
        <path
          d='M94.0698413,53.6380952 C79.2380952,53.6380952 67.2507937,41.6507937 67.2507937,26.8190476 C67.2507937,11.9873016 79.2380952,-7.10542736e-15 94.0698413,-7.10542736e-15 C108.901587,-7.10542736e-15 120.888889,11.9873016 120.888889,26.8190476 L120.888889,53.6380952 L94.0698413,53.6380952 Z M94.0698413,67.2507937 C108.901587,67.2507937 120.888889,79.2380952 120.888889,94.0698413 C120.888889,108.901587 108.901587,120.888889 94.0698413,120.888889 L26.8190476,120.888889 C11.9873016,120.888889 0,108.901587 0,94.0698413 C0,79.2380952 11.9873016,67.2507937 26.8190476,67.2507937 L94.0698413,67.2507937 Z'
          fill='#36C5F0'
        />
        <path
          d='M201.549206,94.0698413 C201.549206,79.2380952 213.536508,67.2507937 228.368254,67.2507937 C243.2,67.2507937 255.187302,79.2380952 255.187302,94.0698413 C255.187302,108.901587 243.2,120.888889 228.368254,120.888889 L201.549206,120.888889 L201.549206,94.0698413 Z M188.139683,94.0698413 C188.139683,108.901587 176.152381,120.888889 161.320635,120.888889 C146.488889,120.888889 134.501587,108.901587 134.501587,94.0698413 L134.501587,26.8190476 C134.501587,11.9873016 146.488889,-1.42108547e-14 161.320635,-1.42108547e-14 C176.152381,-1.42108547e-14 188.139683,11.9873016 188.139683,26.8190476 L188.139683,94.0698413 Z'
          fill='#2EB67D'
        />
        <path
          d='M161.320635,201.549206 C176.152381,201.549206 188.139683,213.536508 188.139683,228.368254 C188.139683,243.2 176.152381,255.187302 161.320635,255.187302 C146.488889,255.187302 134.501587,243.2 134.501587,228.368254 L134.501587,201.549206 L161.320635,201.549206 Z M161.320635,188.139683 C146.488889,188.139683 134.501587,176.152381 134.501587,161.320635 C134.501587,146.488889 146.488889,134.501587 161.320635,134.501587 L228.571429,134.501587 C243.403175,134.501587 255.390476,146.488889 255.390476,161.320635 C255.390476,176.152381 243.403175,188.139683 228.571429,188.139683 L161.320635,188.139683 Z'
          fill='#ECB22E'
        />
      </g>
    </svg>`}
/>

{/* MANUAL-CONTENT-START:intro */}
[Slack](https://www.slack.com/) es una plataforma de comunicación empresarial que ofrece a los equipos un lugar unificado para mensajería, herramientas y archivos.

<iframe
  width="100%"
  height="400"
  src="https://www.youtube.com/embed/J5jz3UaWmE8"
  title="Integración de Slack con Sim"
  frameBorder="0"
  allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
  allowFullScreen
></iframe>

Con Slack, puedes:

- **Automatizar notificaciones de agentes**: Enviar actualizaciones en tiempo real desde tus agentes Sim a cualquier canal de Slack
- **Crear endpoints de webhook**: Configurar bots de Slack como webhooks para activar flujos de trabajo de Sim desde actividades de Slack
- **Mejorar flujos de trabajo de agentes**: Integrar mensajería de Slack en tus agentes para entregar resultados, alertas y actualizaciones de estado
- **Crear y compartir lienzos de Slack**: Generar programáticamente documentos colaborativos (lienzos) en canales de Slack
- **Leer mensajes de canales**: Recuperar y procesar mensajes recientes de cualquier canal de Slack para monitoreo o activación de flujos de trabajo

En Sim, la integración con Slack permite a tus agentes interactuar programáticamente con Slack de varias maneras como parte de sus flujos de trabajo:

- **Enviar mensajes**: Los agentes pueden enviar mensajes formateados a cualquier canal o usuario de Slack, compatible con la sintaxis mrkdwn de Slack para un formato enriquecido.
- **Crear lienzos**: Los agentes pueden crear y compartir lienzos de Slack (documentos colaborativos) directamente en los canales, permitiendo compartir contenido más rico y documentación.
- **Leer mensajes**: Los agentes pueden leer mensajes recientes de los canales, permitiendo monitorear, informar o desencadenar acciones adicionales basadas en la actividad del canal.

Esto permite escenarios de automatización potentes como enviar notificaciones, alertas, actualizaciones e informes directamente al centro de comunicación de tu equipo, compartir documentos estructurados o monitorear conversaciones para desencadenar flujos de trabajo. Tus agentes pueden entregar información oportuna, compartir resultados de procesos que han completado, crear documentos colaborativos o alertar a los miembros del equipo cuando se necesita atención. Esta integración cierra la brecha entre tus flujos de trabajo de IA y la comunicación de tu equipo, asegurando que todos se mantengan informados sin intervención manual. Al conectar Sim con Slack, puedes crear agentes que mantengan a tu equipo actualizado con información relevante en el momento adecuado, mejorar la colaboración compartiendo conocimientos automáticamente y reducir la necesidad de actualizaciones manuales de estado, todo mientras aprovechas tu espacio de trabajo de Slack existente donde tu equipo ya se comunica.
{/* MANUAL-CONTENT-END */}

## Instrucciones de uso

Integración completa con Slack con autenticación OAuth. Envía mensajes formateados usando la sintaxis mrkdwn de Slack o desencadena flujos de trabajo desde eventos de Slack como menciones y mensajes.

## Herramientas

### `slack_message`

Envía mensajes a canales o usuarios de Slack a través de la API de Slack. Compatible con el formato mrkdwn de Slack.

#### Entrada

| Parámetro | Tipo | Obligatorio | Descripción |
| --------- | ---- | -------- | ----------- |
| `authMethod` | string | No | Método de autenticación: oauth o bot_token |
| `botToken` | string | No | Token del bot para Bot personalizado |
| `channel` | string | Sí | Canal de Slack objetivo (p. ej., #general) |
| `text` | string | Sí | Texto del mensaje a enviar (admite formato mrkdwn de Slack) |

#### Salida

| Parámetro | Tipo | Descripción |
| --------- | ---- | ----------- |
| `ts` | string | Marca de tiempo del mensaje |
| `channel` | string | ID del canal donde se envió el mensaje |

### `slack_canvas`

Crea y comparte lienzos de Slack en canales. Los lienzos son documentos colaborativos dentro de Slack.

#### Entrada

| Parámetro | Tipo | Obligatorio | Descripción |
| --------- | ---- | -------- | ----------- |
| `authMethod` | string | No | Método de autenticación: oauth o bot_token |
| `botToken` | string | No | Token del bot para Bot personalizado |
| `channel` | string | Sí | Canal de Slack objetivo (p. ej., #general) |
| `title` | string | Sí | Título del lienzo |
| `content` | string | Sí | Contenido del lienzo en formato markdown |
| `document_content` | object | No | Contenido estructurado del documento de lienzo |

#### Salida

| Parámetro | Tipo | Descripción |
| --------- | ---- | ----------- |
| `canvas_id` | string | ID del lienzo creado |
| `channel` | string | Canal donde se creó el lienzo |
| `title` | string | Título del lienzo |

### `slack_message_reader`

Lee los últimos mensajes de los canales de Slack. Recupera el historial de conversaciones con opciones de filtrado.

#### Entrada

| Parámetro | Tipo | Obligatorio | Descripción |
| --------- | ---- | -------- | ----------- |
| `authMethod` | string | No | Método de autenticación: oauth o bot_token |
| `botToken` | string | No | Token del bot para Bot personalizado |
| `channel` | string | Sí | Canal de Slack del que leer mensajes (p. ej., #general) |
| `limit` | number | No | Número de mensajes a recuperar (predeterminado: 10, máx: 100) |
| `oldest` | string | No | Inicio del rango de tiempo (marca de tiempo) |
| `latest` | string | No | Fin del rango de tiempo (marca de tiempo) |

#### Salida

| Parámetro | Tipo | Descripción |
| --------- | ---- | ----------- |
| `messages` | array | Array de objetos de mensaje del canal |

## Notas

- Categoría: `tools`
- Tipo: `slack`
