---
title: Starter
---

import { Callout } from 'fumadocs-ui/components/callout'
import { Tab, Tabs } from 'fumadocs-ui/components/tabs'
import { Image } from '@/components/ui/image'
import { Video } from '@/components/ui/video'

The Starter block allows you to manually initiate workflow execution with input parameters, offering two input modes: structured parameters or conversational chat.

<div className="flex justify-center">
  <Image
    src="/static/starter.png"
    alt="Starter Block with Manual and Chat Mode Options"
    width={500}
    height={400}
    className="my-6"
  />
</div>

## Execution Modes

Choose your input method from the dropdown:

<Tabs items={['Manual Mode', 'Chat Mode']}>
  <Tab>
    <div className="space-y-4">
      <ul className="list-disc space-y-1 pl-6">
        <li><strong>API Friendly Structured inputs</strong>: Define specific parameters (text, number, boolean, JSON, file, date)</li>
        <li><strong>Testing while Building Your Workflow</strong>: Quick iteration while debugging workflows</li>
      </ul>
      
      <div className="mx-auto w-full overflow-hidden rounded-lg">
        <Video src="input-format.mp4" width={700} height={450} />
      </div>
      
      <p className="text-sm text-gray-600">Configure input parameters that will be available when deploying as an API endpoint.</p>
    </div>
  </Tab>
  <Tab>
    <div className="space-y-4">
      <ul className="list-disc space-y-1 pl-6">
        <li><strong>Natural language</strong>: Users type questions or requests</li>
        <li><strong>Conversational</strong>: Ideal for AI-powered workflows</li>
      </ul>
      
      <div className="mx-auto w-full overflow-hidden rounded-lg">
        <Video src="chat-input.mp4" width={700} height={450} />
      </div>
      
      <p className="text-sm text-gray-600">Chat with your workflow and access input text, conversation ID, and uploaded files for context-aware responses.</p>
    </div>
  </Tab>
</Tabs>

## Using Chat Variables

In Chat mode, access user input and conversation context through special variables:

- **`<start.input>`** - Contains the user's message text
- **`<start.conversationId>`** - Unique identifier for the conversation thread
- **`<start.files>`** - Array of files uploaded by the user (if any)