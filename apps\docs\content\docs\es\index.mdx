---
title: Documentación
---

import { Card, Cards } from 'fumadocs-ui/components/card'

# Documentación de Sim

Bienvenido a Sim, un constructor visual de flujos de trabajo para aplicaciones de IA. Crea potentes agentes de IA, flujos de trabajo de automatización y canales de procesamiento de datos conectando bloques en un lienzo.

## Inicio rápido

<Cards>
  <Card title="Introducción" href="/introduction">
    Aprende lo que puedes construir con Sim
  </Card>
  <Card title="Primeros pasos" href="/getting-started">
    Crea tu primer flujo de trabajo en 10 minutos
  </Card>
  <Card title="Bloques de flujo de trabajo" href="/blocks">
    Aprende sobre los bloques de construcción
  </Card>
  <Card title="Herramientas e integraciones" href="/tools">
    Explora más de 80 integraciones incorporadas
  </Card>
</Cards>

## Conceptos fundamentales

<Cards>
  <Card title="Conexiones" href="/connections">
    Comprende cómo fluyen los datos entre bloques
  </Card>
  <Card title="Variables" href="/variables">
    Trabaja con variables de flujo de trabajo y de entorno
  </Card>
  <Card title="Ejecución" href="/execution">
    Monitoriza las ejecuciones de flujos de trabajo y gestiona costos
  </Card>
  <Card title="Disparadores" href="/triggers">
    Inicia flujos de trabajo mediante API, webhooks o programaciones
  </Card>
</Cards>

## Características avanzadas

<Cards>
  <Card title="Gestión de equipos" href="/permissions/roles-and-permissions">
    Configura roles y permisos de espacio de trabajo
  </Card>
  <Card title="Configuración YAML" href="/yaml">
    Define flujos de trabajo como código
  </Card>
  <Card title="Integración MCP" href="/mcp">
    Conecta servicios externos con el Protocolo de Contexto de Modelo
  </Card>
  <Card title="SDKs" href="/sdks">
    Integra Sim en tus aplicaciones
  </Card>
</Cards>