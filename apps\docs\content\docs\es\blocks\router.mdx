---
title: Router
---

import { Callout } from 'fumadocs-ui/components/callout'
import { Step, Steps } from 'fumadocs-ui/components/steps'
import { Tab, Tabs } from 'fumadocs-ui/components/tabs'
import { Accordion, Accordions } from 'fumadocs-ui/components/accordion'
import { Image } from '@/components/ui/image'
import { Video } from '@/components/ui/video'

El bloque Router utiliza IA para decidir de manera inteligente qué camino debe tomar tu flujo de trabajo a continuación, dirigiendo la ejecución del flujo de trabajo según condiciones o lógica específicas. A diferencia de los bloques de Condición que utilizan reglas simples, los bloques Router pueden entender el contexto y tomar decisiones inteligentes de enrutamiento basadas en el análisis de contenido.

<div className="flex justify-center">
  <Image
    src="/static/blocks/router.png"
    alt="Bloque Router con múltiples caminos"
    width={500}
    height={400}
    className="my-6"
  />
</div>

## Descripción general

El bloque Router te permite:

<Steps>
  <Step>
    <strong>Enrutamiento inteligente de contenido</strong>: Usa IA para entender la intención y el contexto
  </Step>
  <Step>
    <strong>Selección dinámica de rutas</strong>: Dirige flujos de trabajo basados en análisis de contenido no estructurado
  </Step>
  <Step>
    <strong>Decisiones conscientes del contexto</strong>: Toma decisiones inteligentes de enrutamiento más allá de reglas simples
  </Step>
  <Step>
    <strong>Gestión de múltiples rutas</strong>: Maneja flujos de trabajo complejos con múltiples destinos potenciales
  </Step>
</Steps>

## Router vs bloques de Condición

<Accordions>
  <Accordion title="Cuándo usar Router">
    - Se necesita análisis de contenido impulsado por IA
    - Contenido no estructurado o tipos de contenido variables
    - Enrutamiento basado en intención (p. ej., "dirigir tickets de soporte a departamentos")
    - Se requiere toma de decisiones consciente del contexto
  </Accordion>
  <Accordion title="Cuándo usar Condición">
    - Decisiones simples basadas en reglas
    - Datos estructurados o comparaciones numéricas
    - Se necesita enrutamiento rápido y determinista
    - Lógica booleana suficiente
  </Accordion>
</Accordions>

## Cómo funciona

El bloque Router:

<Steps>
  <Step>
    <strong>Analiza el contenido</strong>: Utiliza un LLM para entender el contenido y el contexto de entrada
  </Step>
  <Step>
    <strong>Evalúa objetivos</strong>: Compara el contenido con los bloques de destino disponibles
  </Step>
  <Step>
    <strong>Selecciona el destino</strong>: Identifica la ruta más apropiada basada en la intención
  </Step>
  <Step>
    <strong>Ejecuta el enrutamiento</strong>: Dirige el flujo de trabajo al bloque seleccionado
  </Step>
</Steps>

## Opciones de configuración

### Contenido/Prompt

El contenido o prompt que el Router analizará para tomar decisiones de enrutamiento. Esto puede ser:

- Una consulta o entrada directa del usuario
- Resultado de un bloque anterior
- Un mensaje generado por el sistema

### Bloques de destino

Los posibles bloques de destino entre los que el Router puede seleccionar. El Router detectará automáticamente los bloques conectados, pero también puedes:

- Personalizar las descripciones de los bloques de destino para mejorar la precisión del enrutamiento
- Especificar criterios de enrutamiento para cada bloque de destino
- Excluir ciertos bloques para que no sean considerados como destinos de enrutamiento

### Selección de modelo

Elige un modelo de IA para potenciar la decisión de enrutamiento:

**OpenAI**: GPT-4o, o1, o3, o4-mini, gpt-4.1  \
**Anthropic**: Claude 3.7 Sonnet \
**Google**: Gemini 2.5 Pro, Gemini 2.0 Flash \
**Otros proveedores**: Groq, Cerebras, xAI, DeepSeek \
**Modelos locales**: Cualquier modelo ejecutándose en Ollama 

<div className="w-full max-w-2xl mx-auto overflow-hidden rounded-lg">
  <Video src="router-model-dropdown.mp4" width={500} height={350} />
</div>

**Recomendación**: Utiliza modelos con fuertes capacidades de razonamiento como GPT-4o o Claude 3.7 Sonnet para decisiones de enrutamiento más precisas.

### Clave API

Tu clave API para el proveedor de LLM seleccionado. Esta se almacena de forma segura y se utiliza para la autenticación.

### Acceso a los resultados

Después de que un router tome una decisión, puedes acceder a sus resultados:

- **`<router.prompt>`**: Resumen del prompt de enrutamiento utilizado
- **`<router.selected_path>`**: Detalles del bloque de destino elegido
- **`<router.tokens>`**: Estadísticas de uso de tokens del LLM
- **`<router.cost>`**: Resumen de costos para la llamada de enrutamiento (entrada, salida, total)
- **`<router.model>`**: El modelo utilizado para la toma de decisiones

## Funciones avanzadas

### Criterios de enrutamiento personalizados

Define criterios específicos para cada bloque de destino:

```javascript
// Example routing descriptions
Target Block 1: "Technical support issues, API problems, integration questions"
Target Block 2: "Billing inquiries, subscription changes, payment issues"
Target Block 3: "General questions, feedback, feature requests"
```

## Entradas y salidas

<Tabs items={['Configuración', 'Variables']}>
  <Tab>
    <ul className="list-disc space-y-2 pl-6">
      <li>
        <strong>Contenido/Prompt</strong>: Texto a analizar para decisiones de enrutamiento
      </li>
      <li>
        <strong>Bloques de destino</strong>: Bloques conectados como destinos potenciales
      </li>
      <li>
        <strong>Modelo</strong>: Modelo de IA para análisis de enrutamiento
      </li>
      <li>
        <strong>Clave API</strong>: Autenticación para el proveedor LLM seleccionado
      </li>
    </ul>
  </Tab>
  <Tab>
    <ul className="list-disc space-y-2 pl-6">
      <li>
        <strong>router.prompt</strong>: Resumen del prompt de enrutamiento utilizado
      </li>
      <li>
        <strong>router.selected_path</strong>: Detalles del destino elegido
      </li>
      <li>
        <strong>router.tokens</strong>: Estadísticas de uso de tokens
      </li>
      <li>
        <strong>router.cost</strong>: Resumen de costos para la llamada de enrutamiento (entrada, salida, total)
      </li>
      <li>
        <strong>router.model</strong>: Modelo utilizado para la toma de decisiones
      </li>
    </ul>
  </Tab>
</Tabs>

## Ejemplos de casos de uso

### Clasificación de soporte al cliente

<div className="mb-4 rounded-md border p-4">
  <h4 className="font-medium">Escenario: Enrutar tickets de soporte a departamentos especializados</h4>
  <ol className="list-decimal pl-5 text-sm">
    <li>El usuario envía una solicitud de soporte mediante un formulario</li>
    <li>El enrutador analiza el contenido y contexto del ticket</li>
    <li>Problemas técnicos → Agente de soporte de ingeniería</li>
    <li>Preguntas de facturación → Agente de soporte financiero</li>
  </ol>
</div>

### Clasificación de contenido

<div className="mb-4 rounded-md border p-4">
  <h4 className="font-medium">Escenario: Clasificar y enrutar contenido generado por usuarios</h4>
  <ol className="list-decimal pl-5 text-sm">
    <li>El usuario envía contenido o comentarios</li>
    <li>El enrutador analiza el tipo de contenido y el sentimiento</li>
    <li>Solicitudes de funciones → Flujo de trabajo del equipo de producto</li>
    <li>Informes de errores → Flujo de trabajo de soporte técnico</li>
  </ol>
</div>

### Calificación de leads

<div className="mb-4 rounded-md border p-4">
  <h4 className="font-medium">Escenario: Enrutar leads según criterios de calificación</h4>
  <ol className="list-decimal pl-5 text-sm">
    <li>Información del lead capturada desde un formulario</li>
    <li>El enrutador analiza el tamaño de la empresa, la industria y las necesidades</li>
    <li>Leads empresariales → Equipo de ventas con precios personalizados</li>
    <li>Leads de PYMES → Flujo de incorporación autoservicio</li>
  </ol>
</div>

## Mejores prácticas

- **Proporcionar descripciones claras de destino**: Ayuda al enrutador a entender cuándo seleccionar cada destino con descripciones específicas y detalladas
- **Usar criterios de enrutamiento específicos**: Define condiciones claras y ejemplos para cada ruta para mejorar la precisión
- **Implementar rutas alternativas**: Conecta un destino predeterminado para cuando ninguna ruta específica sea apropiada
- **Probar con entradas diversas**: Asegúrate de que el enrutador maneja varios tipos de entrada, casos extremos y contenido inesperado
- **Monitorear el rendimiento del enrutamiento**: Revisa las decisiones de enrutamiento regularmente y refina los criterios basándote en patrones de uso reales
- **Elegir modelos apropiados**: Utiliza modelos con fuertes capacidades de razonamiento para decisiones de enrutamiento complejas
