---
title: Twilio SMS
description: Send SMS messages
---

import { BlockInfoCard } from "@/components/ui/block-info-card"

<BlockInfoCard 
  type="twilio_sms"
  color="#F22F46"
  icon={true}
  iconSvg={`<svg className="block-icon"  xmlns='http://www.w3.org/2000/svg' viewBox='0 0 256 256'>
      <path
        fill='currentColor'
        d='M128 0c70.656 0 128 57.344 128 128s-57.344 128-128 128S0 198.656 0 128 57.344 0 128 0zm0 33.792c-52.224 0-94.208 41.984-94.208 94.208S75.776 222.208 128 222.208s94.208-41.984 94.208-94.208S180.224 33.792 128 33.792zm31.744 99.328c14.704 0 26.624 11.92 26.624 26.624 0 14.704-11.92 26.624-26.624 26.624-14.704 0-26.624-11.92-26.624-26.624 0-14.704 11.92-26.624 26.624-26.624zm-63.488 0c14.704 0 26.624 11.92 26.624 26.624 0 14.704-11.92 26.624-26.624 26.624-14.704 0-26.624-11.92-26.624-26.624 0-14.704 11.92-26.624 26.624-26.624zm63.488-63.488c14.704 0 26.624 11.92 26.624 26.624 0 14.704-11.92 26.624-26.624 26.624-14.704 0-26.624-11.92-26.624-26.624 0-14.704 11.92-26.624 26.624-26.624zm-63.488 0c14.704 0 26.624 11.92 26.624 26.624 0 14.704-11.92 26.624-26.624 26.624-14.704 0-26.624-11.92-26.624-26.624 0-14.704 11.92-26.624 26.624-26.624z'
      />
    </svg>`}
/>

{/* MANUAL-CONTENT-START:intro */}
[Twilio SMS](https://www.twilio.com/en-us/sms) is a powerful cloud communications platform that enables businesses to integrate messaging capabilities into their applications and services.

Twilio SMS provides a robust API for programmatically sending and receiving text messages globally. With coverage in over 180 countries and a 99.999% uptime SLA, Twilio has established itself as an industry leader in communications technology.

Key features of Twilio SMS include:

- **Global Reach**: Send messages to recipients worldwide with local phone numbers in multiple countries
- **Programmable Messaging**: Customize message delivery with webhooks, delivery receipts, and scheduling options
- **Advanced Analytics**: Track delivery rates, engagement metrics, and optimize your messaging campaigns

In Sim, the Twilio SMS integration enables your agents to leverage these powerful messaging capabilities as part of their workflows. This creates opportunities for sophisticated customer engagement scenarios like appointment reminders, verification codes, alerts, and interactive conversations. The integration bridges the gap between your AI workflows and customer communication channels, allowing your agents to deliver timely, relevant information directly to users' mobile devices. By connecting Sim with Twilio SMS, you can build intelligent agents that engage customers through their preferred communication channel, enhancing user experience while automating routine messaging tasks.
{/* MANUAL-CONTENT-END */}


## Usage Instructions

Send text messages to single or multiple recipients using the Twilio API.



## Tools

### `twilio_send_sms`

Send text messages to single or multiple recipients using the Twilio API.

#### Input

| Parameter | Type | Required | Description |
| --------- | ---- | -------- | ----------- |
| `phoneNumbers` | string | Yes | Phone numbers to send the message to, separated by newlines |
| `message` | string | Yes | Message to send |
| `accountSid` | string | Yes | Twilio Account SID |
| `authToken` | string | Yes | Twilio Auth Token |
| `fromNumber` | string | Yes | Twilio phone number to send the message from |

#### Output

| Parameter | Type | Description |
| --------- | ---- | ----------- |
| `success` | boolean | SMS send success status |
| `messageId` | string | Unique Twilio message identifier \(SID\) |
| `status` | string | Message delivery status from Twilio |
| `fromNumber` | string | Phone number message was sent from |
| `toNumber` | string | Phone number message was sent to |



## Notes

- Category: `tools`
- Type: `twilio_sms`
