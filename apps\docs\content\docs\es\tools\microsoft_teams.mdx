---
title: Microsoft Teams
description: <PERSON>, escribe y crea mensajes
---

import { BlockInfoCard } from "@/components/ui/block-info-card"

<BlockInfoCard 
  type="microsoft_teams"
  color="#E0E0E0"
  icon={true}
  iconSvg={`<svg className="block-icon"  xmlns='http://www.w3.org/2000/svg' viewBox='0 0 2228.833 2073.333'>
      <path
        fill='#5059C9'
        d='M1554.637,777.5h575.713c54.391,0,98.483,44.092,98.483,98.483c0,0,0,0,0,0v524.398 c0,199.901-162.051,361.952-361.952,361.952h0h-1.711c-199.901,0.028-361.975-162-362.004-361.901c0-0.017,0-0.034,0-0.052V828.971 C1503.167,800.544,1526.211,777.5,1554.637,777.5L1554.637,777.5z'
      />
      <circle fill='#5059C9' cx='1943.75' cy='440.583' r='233.25' />
      <circle fill='#7B83EB' cx='1218.083' cy='336.917' r='336.917' />
      <path
        fill='#7B83EB'
        d='M1667.323,777.5H717.01c-53.743,1.33-96.257,45.931-95.01,99.676v598.105 c-7.505,322.519,247.657,590.16,570.167,598.053c322.51-7.893,577.671-275.534,570.167-598.053V877.176 C1763.579,823.431,1721.066,778.83,1667.323,777.5z'
      />
      <path
        opacity='.1'
        d='M1244,777.5v838.145c-0.258,38.435-23.549,72.964-59.09,87.598 c-11.316,4.787-23.478,7.254-35.765,7.257H667.613c-6.738-17.105-12.958-34.21-18.142-51.833 c-18.144-59.477-27.402-121.307-27.472-183.49V877.02c-1.246-53.659,41.198-98.19,94.855-99.52H1244z'
      />
      <path
        opacity='.2'
        d='M1192.167,777.5v889.978c-0.002,12.287-2.47,24.449-7.257,35.765 c-14.634,35.541-49.163,58.833-87.598,59.09H691.975c-8.812-17.105-17.105-34.21-24.362-51.833 c-7.257-17.623-12.958-34.21-18.142-51.833c-18.144-59.476-27.402-121.307-27.472-183.49V877.02 c-1.246-53.659,41.198-98.19,94.855-99.52H1192.167z'
      />
      <path
        opacity='.2'
        d='M1192.167,777.5v786.312c-0.395,52.223-42.632,94.46-94.855,94.855h-447.84 c-18.144-59.476-27.402-121.307-27.472-183.49V877.02c-1.246-53.659,41.198-98.19,94.855-99.52H1192.167z'
      />
      <path
        opacity='.2'
        d='M1140.333,777.5v786.312c-0.395,52.223-42.632,94.46-94.855,94.855H649.472 c-18.144-59.476-27.402-121.307-27.472-183.49V877.02c-1.246-53.659,41.198-98.19,94.855-99.52H1140.333z'
      />
      <path
        opacity='.1'
        d='M1244,509.522v163.275c-8.812,0.518-17.105,1.037-25.917,1.037 c-8.812,0-17.105-0.518-25.917-1.037c-17.496-1.161-34.848-3.937-51.833-8.293c-104.963-24.857-191.679-98.469-233.25-198.003 c-7.153-16.715-12.706-34.071-16.587-51.833h258.648C1201.449,414.866,1243.801,457.217,1244,509.522z'
      />
      <path
        opacity='.2'
        d='M1192.167,561.355v111.442c-17.496-1.161-34.848-3.937-51.833-8.293 c-104.963-24.857-191.679-98.469-233.25-198.003h190.228C1149.616,466.699,1191.968,509.051,1192.167,561.355z'
      />
      <path
        opacity='.2'
        d='M1192.167,561.355v111.442c-17.496-1.161-34.848-3.937-51.833-8.293 c-104.963-24.857-191.679-98.469-233.25-198.003h190.228C1149.616,466.699,1191.968,509.051,1192.167,561.355z'
      />
      <path
        opacity='.2'
        d='M1140.333,561.355v103.148c-104.963-24.857-191.679-98.469-233.25-198.003 h138.395C1097.783,466.699,1140.134,509.051,1140.333,561.355z'
      />
      <linearGradient
        id='a'
        gradientUnits='userSpaceOnUse'
        x1='198.099'
        y1='1683.0726'
        x2='942.2344'
        y2='394.2607'
        gradientTransform='matrix(1 0 0 -1 0 2075.3333)'
      >
        <stop offset='0' stopColor='#5a62c3' />
        <stop offset='.5' stopColor='#4d55bd' />
        <stop offset='1' stopColor='#3940ab' />
        <stop offset='0' stopColor='#5a62c3' />
        <stop offset='.5' stopColor='#4d55bd' />
        <stop offset='1' stopColor='#3940ab' />
      </linearGradient>
      <path
        fill='url(#a)'
        d='M95.01,466.5h950.312c52.473,0,95.01,42.538,95.01,95.01v950.312c0,52.473-42.538,95.01-95.01,95.01 H95.01c-52.473,0-95.01-42.538-95.01-95.01V561.51C0,509.038,42.538,466.5,95.01,466.5z'
      />
      <path
        fill='#FFF'
        d='M820.211,828.193H630.241v517.297H509.211V828.193H320.123V727.844h500.088V828.193z'
      />
    </svg>`}
/>

{/* MANUAL-CONTENT-START:intro */}
[Microsoft Teams](https://teams.microsoft.com) es una robusta plataforma de comunicación y colaboración que permite a los usuarios participar en mensajería en tiempo real, reuniones y compartir contenido dentro de equipos y organizaciones. Como parte del ecosistema de productividad de Microsoft, Microsoft Teams ofrece una funcionalidad de chat perfectamente integrada con Office 365, permitiendo a los usuarios publicar mensajes, coordinar trabajo y mantenerse conectados a través de dispositivos y flujos de trabajo.

Con Microsoft Teams, puedes:

- **Enviar y recibir mensajes**: Comunícate instantáneamente con individuos o grupos en hilos de chat  
- **Colaborar en tiempo real**: Comparte actualizaciones e información entre equipos dentro de canales y chats  
- **Organizar conversaciones**: Mantén el contexto con discusiones encadenadas e historial de chat persistente  
- **Compartir archivos y contenido**: Adjunta y visualiza documentos, imágenes y enlaces directamente en el chat  
- **Integrar con Microsoft 365**: Conéctate perfectamente con Outlook, SharePoint, OneDrive y más  
- **Acceder desde varios dispositivos**: Usa Teams en escritorio, web y móvil con conversaciones sincronizadas en la nube  
- **Comunicación segura**: Aprovecha las funciones de seguridad y cumplimiento de nivel empresarial

En Sim, la integración con Microsoft Teams permite a tus agentes interactuar directamente con los mensajes de chat de forma programática. Esto permite potentes escenarios de automatización como enviar actualizaciones, publicar alertas, coordinar tareas y responder a conversaciones en tiempo real. Tus agentes pueden escribir nuevos mensajes en chats o canales, actualizar contenido basado en datos de flujo de trabajo e interactuar con usuarios donde ocurre la colaboración. Al integrar Sim con Microsoft Teams, reduces la brecha entre flujos de trabajo inteligentes y comunicación de equipo, permitiendo a tus agentes agilizar la colaboración, automatizar tareas de comunicación y mantener a tus equipos alineados.
{/* MANUAL-CONTENT-END */}

## Instrucciones de uso

Integra la funcionalidad de Microsoft Teams para gestionar mensajes. Lee contenido de mensajes existentes y escribe mensajes usando autenticación OAuth. Compatible con manipulación de contenido de texto para creación y edición de mensajes.

## Herramientas

### `microsoft_teams_read_chat`

Leer contenido de un chat de Microsoft Teams

#### Entrada

| Parámetro | Tipo | Obligatorio | Descripción |
| --------- | ---- | ----------- | ----------- |
| `chatId` | string | Sí | El ID del chat del que leer |

#### Salida

| Parámetro | Tipo | Descripción |
| --------- | ---- | ----------- |
| `success` | boolean | Estado de éxito de la operación de lectura del chat de Teams |
| `messageCount` | number | Número de mensajes recuperados del chat |
| `chatId` | string | ID del chat del que se leyó |
| `messages` | array | Array de objetos de mensajes de chat |
| `attachmentCount` | number | Número total de archivos adjuntos encontrados |
| `attachmentTypes` | array | Tipos de archivos adjuntos encontrados |
| `content` | string | Contenido formateado de los mensajes de chat |

### `microsoft_teams_write_chat`

Escribir o actualizar contenido en un chat de Microsoft Teams

#### Entrada

| Parámetro | Tipo | Obligatorio | Descripción |
| --------- | ---- | -------- | ----------- |
| `chatId` | string | Sí | El ID del chat donde escribir |
| `content` | string | Sí | El contenido a escribir en el mensaje |

#### Salida

| Parámetro | Tipo | Descripción |
| --------- | ---- | ----------- |
| `success` | boolean | Estado de éxito del envío del mensaje de chat de Teams |
| `messageId` | string | Identificador único para el mensaje enviado |
| `chatId` | string | ID del chat donde se envió el mensaje |
| `createdTime` | string | Marca de tiempo cuando se creó el mensaje |
| `url` | string | URL web del mensaje |
| `updatedContent` | boolean | Si el contenido se actualizó correctamente |

### `microsoft_teams_read_channel`

Leer contenido de un canal de Microsoft Teams

#### Entrada

| Parámetro | Tipo | Obligatorio | Descripción |
| --------- | ---- | -------- | ----------- |
| `teamId` | string | Sí | El ID del equipo del que leer |
| `channelId` | string | Sí | El ID del canal del que leer |

#### Salida

| Parámetro | Tipo | Descripción |
| --------- | ---- | ----------- |
| `success` | boolean | Estado de éxito de la operación de lectura del canal de Teams |
| `messageCount` | number | Número de mensajes recuperados del canal |
| `teamId` | string | ID del equipo del que se leyó |
| `channelId` | string | ID del canal del que se leyó |
| `messages` | array | Array de objetos de mensajes del canal |
| `attachmentCount` | number | Número total de archivos adjuntos encontrados |
| `attachmentTypes` | array | Tipos de archivos adjuntos encontrados |
| `content` | string | Contenido formateado de los mensajes del canal |

### `microsoft_teams_write_channel`

Escribir o enviar un mensaje a un canal de Microsoft Teams

#### Entrada

| Parámetro | Tipo | Obligatorio | Descripción |
| --------- | ---- | -------- | ----------- |
| `teamId` | string | Sí | El ID del equipo al que escribir |
| `channelId` | string | Sí | El ID del canal al que escribir |
| `content` | string | Sí | El contenido para escribir en el canal |

#### Salida

| Parámetro | Tipo | Descripción |
| --------- | ---- | ----------- |
| `success` | boolean | Estado de éxito del envío del mensaje al canal de Teams |
| `messageId` | string | Identificador único para el mensaje enviado |
| `teamId` | string | ID del equipo donde se envió el mensaje |
| `channelId` | string | ID del canal donde se envió el mensaje |
| `createdTime` | string | Marca de tiempo cuando se creó el mensaje |
| `url` | string | URL web del mensaje |
| `updatedContent` | boolean | Si el contenido se actualizó correctamente |

## Notas

- Categoría: `tools`
- Tipo: `microsoft_teams`
