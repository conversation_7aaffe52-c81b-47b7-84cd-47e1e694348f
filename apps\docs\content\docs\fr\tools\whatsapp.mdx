---
title: WhatsApp
description: Envoyer des messages WhatsApp
---

import { BlockInfoCard } from "@/components/ui/block-info-card"

<BlockInfoCard 
  type="whatsapp"
  color="#25D366"
  icon={true}
  iconSvg={`<svg className="block-icon"
      
      xmlns='http://www.w3.org/2000/svg'
      
      
      fill='currentColor'
      viewBox='0 0 16 16'
    >
      <path d='M13.601 2.326A7.85 7.85 0 0 0 7.994 0C3.627 0 .068 3.558.064 7.926c0 1.399.366 2.76 1.057 3.965L0 16l4.204-1.102a7.9 7.9 0 0 0 3.79.965h.004c4.368 0 7.926-3.558 7.93-7.93A7.9 7.9 0 0 0 13.6 2.326zM7.994 14.521a6.6 6.6 0 0 1-3.356-.92l-.24-.144-2.494.654.666-2.433-.156-.251a6.56 6.56 0 0 1-1.007-3.505c0-3.626 2.957-6.584 6.591-6.584a6.56 6.56 0 0 1 4.66 1.931 6.56 6.56 0 0 1 1.928 4.66c-.004 3.639-2.961 6.592-6.592 6.592m3.615-4.934c-.197-.099-1.17-.578-1.353-.646-.182-.065-.315-.099-.445.099-.133.197-.513.646-.627.775-.114.133-.232.148-.43.05-.197-.1-.836-.308-1.592-.985-.59-.525-.985-1.175-1.103-1.372-.114-.198-.011-.304.088-.403.087-.088.197-.232.296-.346.1-.114.133-.198.198-.33.065-.134.034-.248-.015-.347-.05-.099-.445-1.076-.612-1.47-.16-.389-.323-.335-.445-.34-.114-.007-.247-.007-.38-.007a.73.73 0 0 0-.529.247c-.182.198-.691.677-.691 1.654s.71 1.916.81 2.049c.098.133 1.394 2.132 3.383 2.992.47.205.84.326 1.129.418.475.152.904.129 1.246.08.38-.058 1.171-.48 1.338-.943.164-.464.164-.86.114-.943-.049-.084-.182-.133-.38-.232' />
    </svg>`}
/>

{/* MANUAL-CONTENT-START:intro */}
[WhatsApp](https://www.whatsapp.com/) est une plateforme de messagerie mondialement populaire qui permet une communication sécurisée et fiable entre les individus et les entreprises.

L'API WhatsApp Business offre aux organisations de puissantes capacités pour :

- **Engager les clients** : envoyer des messages personnalisés, des notifications et des mises à jour directement sur l'application de messagerie préférée des clients
- **Automatiser les conversations** : créer des chatbots interactifs et des systèmes de réponse automatisés pour les demandes courantes
- **Améliorer le support** : fournir un service client en temps réel via une interface familière avec prise en charge de médias riches
- **Générer des conversions** : faciliter les transactions et les suivis avec les clients dans un environnement sécurisé et conforme

Dans Sim, l'intégration WhatsApp permet à vos agents d'exploiter ces capacités de messagerie dans le cadre de leurs flux de travail. Cela crée des opportunités pour des scénarios sophistiqués d'engagement client comme les rappels de rendez-vous, les codes de vérification, les alertes et les conversations interactives. L'intégration comble le fossé entre vos flux de travail IA et les canaux de communication client, permettant à vos agents de délivrer des informations opportunes et pertinentes directement sur les appareils mobiles des utilisateurs. En connectant Sim avec WhatsApp, vous pouvez construire des agents intelligents qui engagent les clients via leur plateforme de messagerie préférée, améliorant l'expérience utilisateur tout en automatisant les tâches de messagerie routinières.
{/* MANUAL-CONTENT-END */}

## Instructions d'utilisation

Envoyez des messages aux utilisateurs WhatsApp en utilisant l'API WhatsApp Business. Nécessite une configuration de l'API WhatsApp Business.

## Outils

### `whatsapp_send_message`

Envoyer des messages WhatsApp

#### Entrée

| Paramètre | Type | Obligatoire | Description |
| --------- | ---- | ---------- | ----------- |
| `phoneNumber` | chaîne | Oui | Numéro de téléphone du destinataire avec l'indicatif du pays |
| `message` | chaîne | Oui | Contenu du message à envoyer |
| `phoneNumberId` | chaîne | Oui | ID du numéro de téléphone WhatsApp Business |

#### Sortie

| Paramètre | Type | Description |
| --------- | ---- | ----------- |
| `success` | booléen | Statut de réussite d'envoi du message WhatsApp |
| `messageId` | chaîne | Identifiant unique du message WhatsApp |
| `phoneNumber` | chaîne | Numéro de téléphone du destinataire |
| `status` | chaîne | Statut de livraison du message |
| `timestamp` | chaîne | Horodatage d'envoi du message |

## Remarques

- Catégorie : `tools`
- Type : `whatsapp`
