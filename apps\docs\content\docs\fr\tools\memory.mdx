---
title: Mémoire
description: Ajouter un stockage de mémoire
---

import { BlockInfoCard } from "@/components/ui/block-info-card"

<BlockInfoCard 
  type="memory"
  color="#F64F9E"
  icon={true}
  iconSvg={`<svg className="block-icon"
      
      xmlns='http://www.w3.org/2000/svg'
      
      
      viewBox='0 0 24 24'
      fill='none'
      stroke='currentColor'
      strokeWidth='2'
      strokeLinecap='round'
      strokeLinejoin='round'
    >
      <path d='M12 5a3 3 0 1 0-5.997.125 4 4 0 0 0-2.526 5.77 4 4 0 0 0 .556 6.588A4 4 0 1 0 12 18Z' />
      <path d='M12 5a3 3 0 1 1 5.997.125 4 4 0 0 1 2.526 5.77 4 4 0 0 1-.556 6.588A4 4 0 1 1 12 18Z' />
      <path d='M15 13a4.5 4.5 0 0 1-3-4 4.5 4.5 0 0 1-3 4' />
      <path d='M17.599 6.5a3 3 0 0 0 .399-1.375' />
      <path d='M6.003 5.125A3 3 0 0 0 6.401 6.5' />
      <path d='M3.477 10.896a4 4 0 0 1 .585-.396' />
      <path d='M19.938 10.5a4 4 0 0 1 .585.396' />
      <path d='M6 18a4 4 0 0 1-1.967-.516' />
      <path d='M19.967 17.484A4 4 0 0 1 18 18' />
    </svg>`}
/>

## Instructions d'utilisation

Créez un stockage persistant pour les données qui doivent être accessibles à travers plusieurs étapes du workflow. Stockez et récupérez des informations tout au long de l'exécution de votre workflow pour maintenir le contexte et l'état.

## Outils

### `memory_add`

Ajoutez une nouvelle mémoire à la base de données ou complétez une mémoire existante avec le même ID.

#### Entrée

| Paramètre | Type | Obligatoire | Description |
| --------- | ---- | ----------- | ----------- |
| `id` | string | Oui | Identifiant pour la mémoire. Si une mémoire avec cet ID existe déjà, les nouvelles données y seront ajoutées. |
| `role` | string | Oui | Rôle pour la mémoire de l'agent \(user, assistant, ou system\) |
| `content` | string | Oui | Contenu pour la mémoire de l'agent |

#### Sortie

| Paramètre | Type | Description |
| --------- | ---- | ----------- |
| `success` | boolean | Indique si la mémoire a été ajoutée avec succès |
| `memories` | array | Tableau d'objets de mémoire incluant la nouvelle mémoire ou celle mise à jour |
| `error` | string | Message d'erreur si l'opération a échoué |

### `memory_get`

Récupérer une mémoire spécifique par son identifiant

#### Entrée

| Paramètre | Type | Obligatoire | Description |
| --------- | ---- | ----------- | ----------- |
| `id` | string | Oui | Identifiant de la mémoire à récupérer |

#### Sortie

| Paramètre | Type | Description |
| --------- | ---- | ----------- |
| `success` | boolean | Indique si la mémoire a été récupérée avec succès |
| `memories` | array | Tableau de données de mémoire pour l'identifiant demandé |
| `message` | string | Message de succès ou d'erreur |
| `error` | string | Message d'erreur si l'opération a échoué |

### `memory_get_all`

Récupérer toutes les mémoires de la base de données

#### Entrée

| Paramètre | Type | Obligatoire | Description |
| --------- | ---- | ----------- | ----------- |

#### Sortie

| Paramètre | Type | Description |
| --------- | ---- | ----------- |
| `success` | boolean | Indique si toutes les mémoires ont été récupérées avec succès |
| `memories` | array | Tableau de tous les objets de mémoire avec leurs clés, types et données |
| `message` | string | Message de succès ou d'erreur |
| `error` | string | Message d'erreur si l'opération a échoué |

### `memory_delete`

Supprimer un souvenir spécifique par son ID

#### Entrée

| Paramètre | Type | Obligatoire | Description |
| --------- | ---- | ---------- | ----------- |
| `id` | chaîne | Oui | Identifiant du souvenir à supprimer |

#### Sortie

| Paramètre | Type | Description |
| --------- | ---- | ----------- |
| `success` | booléen | Indique si le souvenir a été supprimé avec succès |
| `message` | chaîne | Message de succès ou d'erreur |
| `error` | chaîne | Message d'erreur si l'opération a échoué |

## Notes

- Catégorie : `blocks`
- Type : `memory`
