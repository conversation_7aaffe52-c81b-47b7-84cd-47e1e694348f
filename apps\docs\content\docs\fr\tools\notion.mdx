---
title: Notion
description: <PERSON><PERSON><PERSON> les pages Notion
---

import { BlockInfoCard } from "@/components/ui/block-info-card"

<BlockInfoCard 
  type="notion"
  color="#181C1E"
  icon={true}
  iconSvg={`<svg className="block-icon" xmlns='http://www.w3.org/2000/svg' viewBox='0 0 50 50'   >
      <path
        d='M31.494141 5.1503906L5.9277344 7.0019531A1.0001 1.0001 0 005.9042969 7.0039062A1.0001 1.0001 0 005.8652344 7.0097656A1.0001 1.0001 0 005.7929688 7.0214844A1.0001 1.0001 0 005.7636719 7.0292969A1.0001 1.0001 0 005.7304688 7.0371094A1.0001 1.0001 0 005.6582031 7.0605469A1.0001 1.0001 0 005.6113281 7.0800781A1.0001 1.0001 0 005.5839844 7.0917969A1.0001 1.0001 0 005.4335938 7.1777344A1.0001 1.0001 0 005.4082031 7.1933594A1.0001 1.0001 0 005.3476562 7.2421875A1.0001 1.0001 0 005.3359375 7.2539062A1.0001 1.0001 0 005.2871094 7.2988281A1.0001 1.0001 0 005.2578125 7.3320312A1.0001 1.0001 0 005.2148438 7.3828125A1.0001 1.0001 0 005.1992188 7.4023438A1.0001 1.0001 0 005.15625 7.4648438A1.0001 1.0001 0 005.1445312 7.484375A1.0001 1.0001 0 005.1074219 7.5488281A1.0001 1.0001 0 005.09375 7.5761719A1.0001 1.0001 0 005.0644531 7.6484375A1.0001 1.0001 0 005.0605469 7.65625A1.0001 1.0001 0 005.015625 7.8300781A1.0001 1.0001 0 005.0097656 7.8613281A1.0001 1.0001 0 005.0019531 7.9414062A1.0001 1.0001 0 005.0019531 7.9453125A1.0001 1.0001 0 005 8L5 33.738281C5 34.76391 5.3151542 35.766862 5.9042969 36.607422A1.0001 1.0001 0 005.953125 36.671875L12.126953 44.101562A1.0001 1.0001 0 0012.359375 44.382812L12.75 44.851562A1.0006635 1.0006635 0 0012.917969 45.011719C13.50508 45.581386 14.317167 45.917563 15.193359 45.861328L42.193359 44.119141C43.762433 44.017718 45 42.697027 45 41.125L45 15.132812C45 14.209354 44.565523 13.390672 43.904297 12.839844A1.0008168 1.0008168 0 0043.748047 12.695312L43.263672 12.337891A1.0001 1.0001 0 0043.0625 12.189453L34.824219 6.1132812C33.865071 5.4054876 32.682705 5.0641541 31.494141 5.1503906zM31.638672 7.1445312C32.352108 7.0927682 33.061867 7.29845 33.636719 7.7226562L39.767578 12.246094L14.742188 13.884766C13.880567 13.941006 13.037689 13.622196 12.425781 13.011719L12.423828 13.011719L8.2539062 8.8398438L31.638672 7.1445312zM7 10.414062L11.011719 14.425781L12 15.414062L12 40.818359L7.5390625 35.449219C7.1899317 34.947488 7 34.351269 7 33.738281L7 10.414062zM41.935547 14.134766C42.526748 14.096822 43 14.54116 43 15.132812L43 41.125C43 41.660973 42.59938 42.08847 42.064453 42.123047L15.064453 43.865234C14.770856 43.884078 14.506356 43.783483 14.314453 43.605469A1.0006635 1.0006635 0 0014.3125 43.603516C14.3125 43.603516 14.310547 43.601562 14.310547 43.601562C14.306465 43.597733 14.304796 43.59179 14.300781 43.587891A1.0006635 1.0006635 0 0014.289062 43.572266C14.112238 43.393435 14 43.149431 14 42.867188L14 16.875C14 16.337536 14.39999 15.911571 14.935547 15.876953L41.935547 14.134766zM38.496094 19L33.421875 19.28125C32.647875 19.36125 31.746094 19.938 31.746094 20.875L33.996094 21.0625L33.996094 31.753906L26.214844 19.751953L20.382812 20.080078C19.291812 20.160078 18.994141 20.970953 18.994141 22.001953L21.244141 22.001953L21.244141 37.566406C21.244141 37.566406 20.191844 37.850406 19.839844 37.941406C19.091844 38.134406 18.994141 38.784906 18.994141 39.253906C18.994141 39.253906 22.746656 39.065547 24.472656 38.935547C26.431656 38.785547 26.496094 37.472656 26.496094 37.472656L24.246094 37.003906L24.246094 25.470703C24.246094 25.470703 29.965844 34.660328 31.714844 37.361328C32.537844 38.630328 33.152375 38.878906 34.234375 38.878906C35.122375 38.878906 35.962141 38.616594 36.994141 38.058594L36.994141 20.697266C36.994141 20.697266 37.184203 20.687141 37.783203 20.494141C38.466203 20.273141 38.496094 19.656 38.496094 19z'
        fill='currentColor'
      />
    </svg>`}
/>

{/* MANUAL-CONTENT-START:intro */}
[Notion](https://www.notion.so) est un espace de travail tout-en-un qui combine notes, documents, wikis et outils de gestion de projet en une seule plateforme. Il offre un environnement flexible et personnalisable où les utilisateurs peuvent créer, organiser et collaborer sur du contenu dans différents formats.

Avec Notion, vous pouvez :

- **Créer du contenu polyvalent** : élaborer des documents, wikis, bases de données, tableaux kanban, calendriers et plus encore
- **Organiser l'information** : structurer le contenu de façon hiérarchique avec des pages imbriquées et des bases de données puissantes
- **Collaborer en toute fluidité** : partager des espaces de travail et des pages avec les membres de l'équipe pour une collaboration en temps réel
- **Personnaliser votre espace de travail** : concevoir votre flux de travail idéal avec des modèles flexibles et des blocs de construction
- **Connecter les informations** : créer des liens entre les pages et les bases de données pour établir un réseau de connaissances
- **Accéder partout** : utiliser Notion sur le web, l'ordinateur et les plateformes mobiles avec synchronisation automatique

Dans Sim, l'intégration de Notion permet à vos agents d'interagir directement avec votre espace de travail Notion de manière programmatique. Cela permet des scénarios d'automatisation puissants tels que la gestion des connaissances, la création de contenu et la récupération d'informations. Vos agents peuvent :

- **Lire des pages Notion** : extraire du contenu et des métadonnées de n'importe quelle page Notion.
- **Lire des bases de données Notion** : récupérer la structure et les informations des bases de données.
- **Écrire sur des pages** : ajouter du nouveau contenu aux pages Notion existantes.
- **Créer de nouvelles pages** : générer de nouvelles pages Notion sous une page parente, avec des titres et du contenu personnalisés.
- **Interroger des bases de données** : rechercher et filtrer les entrées de base de données à l'aide de critères de filtrage et de tri avancés.
- **Rechercher dans l'espace de travail** : rechercher dans l'ensemble de votre espace de travail Notion des pages ou des bases de données correspondant à des requêtes spécifiques.
- **Créer de nouvelles bases de données** : créer programmatiquement de nouvelles bases de données avec des propriétés et une structure personnalisées.

Cette intégration comble le fossé entre vos flux de travail IA et votre base de connaissances, permettant une gestion transparente de la documentation et des informations. En connectant Sim à Notion, vous pouvez automatiser les processus de documentation, maintenir des référentiels d'informations à jour, générer des rapports et organiser intelligemment les informations, le tout grâce à vos agents intelligents.
{/* MANUAL-CONTENT-END */}

## Instructions d'utilisation

Intégrez avec Notion pour lire le contenu des pages, écrire du nouveau contenu et créer de nouvelles pages.

## Outils

### `notion_read`

Lire le contenu d'une page Notion

#### Entrée

| Paramètre | Type | Obligatoire | Description |
| --------- | ---- | ----------- | ----------- |
| `pageId` | chaîne | Oui | L'ID de la page Notion à lire |

#### Sortie

| Paramètre | Type | Description |
| --------- | ---- | ----------- |
| `content` | chaîne | Contenu de la page au format markdown avec en-têtes, paragraphes, listes et tâches |
| `metadata` | objet | Métadonnées de la page incluant titre, URL et horodatages |

### `notion_read_database`

Lire les informations et la structure d'une base de données Notion

#### Entrée

| Paramètre | Type | Obligatoire | Description |
| --------- | ---- | ----------- | ----------- |
| `databaseId` | chaîne | Oui | L'ID de la base de données Notion à lire |

#### Sortie

| Paramètre | Type | Description |
| --------- | ---- | ----------- |
| `content` | chaîne | Informations de la base de données incluant titre, schéma des propriétés et métadonnées |
| `metadata` | objet | Métadonnées de la base de données incluant titre, ID, URL, horodatages et schéma des propriétés |

### `notion_write`

Ajouter du contenu à une page Notion

#### Entrée

| Paramètre | Type | Obligatoire | Description |
| --------- | ---- | ----------- | ----------- |
| `pageId` | chaîne | Oui | L'ID de la page Notion à laquelle ajouter du contenu |
| `content` | chaîne | Oui | Le contenu à ajouter à la page |

#### Sortie

| Paramètre | Type | Description |
| --------- | ---- | ----------- |
| `content` | chaîne | Message de succès confirmant que le contenu a été ajouté à la page |

### `notion_create_page`

Créer une nouvelle page dans Notion

#### Entrée

| Paramètre | Type | Obligatoire | Description |
| --------- | ---- | ----------- | ----------- |
| `parentId` | chaîne | Oui | ID de la page parente |
| `title` | chaîne | Non | Titre de la nouvelle page |
| `content` | chaîne | Non | Contenu optionnel à ajouter à la page lors de sa création |

#### Sortie

| Paramètre | Type | Description |
| --------- | ---- | ----------- |
| `content` | chaîne | Message de succès confirmant la création de la page |
| `metadata` | objet | Métadonnées de la page incluant le titre, l'ID de la page, l'URL et les horodatages |

### `notion_query_database`

Interroger et filtrer les entrées de base de données Notion avec filtrage avancé

#### Entrée

| Paramètre | Type | Obligatoire | Description |
| --------- | ---- | ----------- | ----------- |
| `databaseId` | chaîne | Oui | L'ID de la base de données à interroger |
| `filter` | chaîne | Non | Conditions de filtrage au format JSON \(facultatif\) |
| `sorts` | chaîne | Non | Critères de tri sous forme de tableau JSON \(facultatif\) |
| `pageSize` | nombre | Non | Nombre de résultats à retourner \(par défaut : 100, max : 100\) |

#### Sortie

| Paramètre | Type | Description |
| --------- | ---- | ----------- |
| `content` | chaîne | Liste formatée des entrées de la base de données avec leurs propriétés |
| `metadata` | objet | Métadonnées de la requête incluant le nombre total de résultats, les informations de pagination et le tableau de résultats bruts |

### `notion_search`

Rechercher dans toutes les pages et bases de données de l'espace de travail Notion

#### Entrée

| Paramètre | Type | Obligatoire | Description |
| --------- | ---- | ---------- | ----------- |
| `query` | chaîne | Non | Termes de recherche \(laisser vide pour obtenir toutes les pages\) |
| `filterType` | chaîne | Non | Filtrer par type d'objet : page, database, ou laisser vide pour tous |
| `pageSize` | nombre | Non | Nombre de résultats à retourner \(par défaut : 100, max : 100\) |

#### Sortie

| Paramètre | Type | Description |
| --------- | ---- | ----------- |
| `content` | chaîne | Liste formatée des résultats de recherche incluant pages et bases de données |
| `metadata` | objet | Métadonnées de recherche incluant le nombre total de résultats, les informations de pagination et le tableau des résultats bruts |

### `notion_create_database`

Créer une nouvelle base de données dans Notion avec des propriétés personnalisées

#### Entrée

| Paramètre | Type | Obligatoire | Description |
| --------- | ---- | ---------- | ----------- |
| `parentId` | chaîne | Oui | ID de la page parente où la base de données sera créée |
| `title` | chaîne | Oui | Titre pour la nouvelle base de données |
| `properties` | chaîne | Non | Propriétés de la base de données sous forme d'objet JSON \(facultatif, créera une propriété "Name" par défaut si vide\) |

#### Sortie

| Paramètre | Type | Description |
| --------- | ---- | ----------- |
| `content` | chaîne | Message de succès avec les détails de la base de données et la liste des propriétés |
| `metadata` | objet | Métadonnées de la base de données incluant l'ID, le titre, l'URL, l'heure de création et le schéma des propriétés |

## Notes

- Catégorie : `tools`
- Type : `notion`
