---
title: Introduction
---

import { Card, Cards } from 'fumadocs-ui/components/card'
import { Callout } from 'fumadocs-ui/components/callout'
import { Image } from '@/components/ui/image'

Sim is a visual workflow builder for AI applications that lets you build AI agent workflows visually. Create powerful AI agents, automation workflows, and data processing pipelines by connecting blocks on a canvas—no coding required.

<div className="flex justify-center">
  <Image
    src="/static/introduction.png"
    alt="Sim visual workflow canvas"
    width={700}
    height={450}
    className="my-6"
  />
</div>

## What You Can Build

**AI Assistants & Chatbots**
Create intelligent agents that can search the web, access your calendar, send emails, and interact with your business tools.

**Business Process Automation**
Automate repetitive tasks like data entry, report generation, customer support responses, and content creation.

**Data Processing & Analysis**
Extract insights from documents, analyze datasets, generate reports, and sync data between systems.

**API Integration Workflows**
Connect multiple services into unified endpoints, orchestrate complex business logic, and handle event-driven automation.

## How It Works

**Visual Canvas**
Drag and drop blocks to build workflows. Connect AI models, databases, APIs, and business tools with simple point-and-click connections.

**Smart Blocks**
Choose from processing blocks (AI agents, APIs, functions), logic blocks (conditions, loops, routers), and output blocks (responses, evaluators).

**Multiple Triggers**
Start workflows via chat interface, REST API, webhooks, scheduled jobs, or external events from services like Slack and GitHub.

**Team Collaboration**
Work simultaneously with team members on the same workflow with real-time editing and permissions management.

## Built-in Integrations

Sim connects to 80+ services out of the box:

- **AI Models**: OpenAI, Anthropic, Google, Groq, Cerebras, local Ollama models
- **Communication**: Gmail, Slack, Teams, Telegram, WhatsApp  
- **Productivity**: Notion, Google Sheets, Airtable, Monday.com
- **Development**: GitHub, Jira, Linear, browser automation
- **Search & Web**: Google Search, Perplexity, Firecrawl, Exa AI
- **Databases**: PostgreSQL, MySQL, Supabase, Pinecone, Qdrant

Need something custom? Use our [MCP integration](/mcp) to connect any external service.

## Deployment Options

**Cloud-hosted**: Get started instantly at [sim.ai](https://sim.ai) with managed infrastructure, automatic scaling, and built-in monitoring.

**Self-hosted**: Deploy on your own infrastructure using Docker, with support for local AI models via Ollama for complete data privacy.

## Next Steps

Ready to build your first AI workflow?

<Cards>
  <Card title="Getting Started" href="/getting-started">
    Create your first workflow in 10 minutes
  </Card>
  <Card title="Workflow Blocks" href="/blocks">
    Learn about the building blocks
  </Card>
  <Card title="Tools & Integrations" href="/tools">
    Explore 60+ built-in integrations
  </Card>
  <Card title="Team Permissions" href="/permissions/roles-and-permissions">
    Set up workspace roles and permissions
  </Card>
</Cards>
