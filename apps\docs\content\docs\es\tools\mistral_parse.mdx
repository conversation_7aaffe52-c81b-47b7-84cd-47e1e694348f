---
title: Mistral Parser
description: Extraer texto de documentos PDF
---

import { BlockInfoCard } from "@/components/ui/block-info-card"

<BlockInfoCard 
  type="mistral_parse"
  color="#000000"
  icon={true}
  iconSvg={`<svg className="block-icon"
      
      
      
      viewBox='1 0.5 24 22'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
      preserveAspectRatio='xMidYMid meet'
    >
      <g clipPath='url(#clip0_1621_58)'>
        <path d='M17.4541 0H21.8177V4.39481H17.4541V0Z' fill='black' />
        <path d='M19.6367 0H24.0003V4.39481H19.6367V0Z' fill='#F7D046' />
        <path
          d='M0 0H4.36359V4.39481H0V0ZM0 4.39481H4.36359V8.78961H0V4.39481ZM0 8.78971H4.36359V13.1845H0V8.78971ZM0 13.1845H4.36359V17.5793H0V13.1845ZM0 17.5794H4.36359V21.9742H0V17.5794Z'
          fill='black'
        />
        <path d='M2.18164 0H6.54523V4.39481H2.18164V0Z' fill='#F7D046' />
        <path
          d='M19.6362 4.39478H23.9998V8.78958H19.6362V4.39478ZM2.18164 4.39478H6.54523V8.78958H2.18164V4.39478Z'
          fill='#F2A73B'
        />
        <path d='M13.0908 4.39478H17.4544V8.78958H13.0908V4.39478Z' fill='black' />
        <path
          d='M15.2732 4.39478H19.6368V8.78958H15.2732V4.39478ZM6.5459 4.39478H10.9095V8.78958H6.5459V4.39478Z'
          fill='#F2A73B'
        />
        <path
          d='M10.9096 8.78979H15.2732V13.1846H10.9096V8.78979ZM15.2732 8.78979H19.6368V13.1846H15.2732V8.78979ZM6.5459 8.78979H10.9096V13.1846H6.5459V8.78979Z'
          fill='#EE792F'
        />
        <path d='M8.72754 13.1846H13.0911V17.5794H8.72754V13.1846Z' fill='black' />
        <path d='M10.9092 13.1846H15.2728V17.5794H10.9092V13.1846Z' fill='#EB5829' />
        <path
          d='M19.6362 8.78979H23.9998V13.1846H19.6362V8.78979ZM2.18164 8.78979H6.54523V13.1846H2.18164V8.78979Z'
          fill='#EE792F'
        />
        <path d='M17.4541 13.1846H21.8177V17.5794H17.4541V13.1846Z' fill='black' />
        <path d='M19.6367 13.1846H24.0003V17.5794H19.6367V13.1846Z' fill='#EB5829' />
        <path d='M17.4541 17.5793H21.8177V21.9742H17.4541V17.5793Z' fill='black' />
        <path d='M2.18164 13.1846H6.54523V17.5794H2.18164V13.1846Z' fill='#EB5829' />
        <path
          d='M19.6362 17.5793H23.9998V21.9742H19.6362V17.5793ZM2.18164 17.5793H6.54523V21.9742H2.18164V17.5793Z'
          fill='#EA3326'
        />
      </g>
      <defs>
        <clipPath id='clip0_1621_58'>
          <rect   fill='white' />
        </clipPath>
      </defs>
    </svg>`}
/>

{/* MANUAL-CONTENT-START:intro */}
La herramienta Mistral Parse proporciona una forma potente de extraer y procesar contenido de documentos PDF utilizando la [API de OCR de Mistral](https://mistral.ai/). Esta herramienta aprovecha el reconocimiento óptico de caracteres avanzado para extraer con precisión texto y estructura de archivos PDF, facilitando la incorporación de datos de documentos en los flujos de trabajo de tus agentes.

Con la herramienta Mistral Parse, puedes:

- **Extraer texto de PDFs**: Convertir con precisión el contenido de PDF a formatos de texto, markdown o JSON
- **Procesar PDFs desde URLs**: Extraer directamente contenido de PDFs alojados en línea proporcionando sus URLs
- **Mantener la estructura del documento**: Preservar el formato, tablas y diseño de los PDFs originales
- **Extraer imágenes**: Incluir opcionalmente imágenes incrustadas de los PDFs
- **Seleccionar páginas específicas**: Procesar solo las páginas que necesitas de documentos de múltiples páginas

La herramienta Mistral Parse es particularmente útil para escenarios donde tus agentes necesitan trabajar con contenido PDF, como analizar informes, extraer datos de formularios o procesar texto de documentos escaneados. Simplifica el proceso de hacer que el contenido PDF esté disponible para tus agentes, permitiéndoles trabajar con información almacenada en PDFs tan fácilmente como con entrada de texto directa.
{/* MANUAL-CONTENT-END */}

## Instrucciones de uso

Extrae texto y estructura de documentos PDF utilizando la API de OCR de Mistral. Introduce una URL a un documento PDF o sube un archivo PDF directamente. Configura las opciones de procesamiento y obtén el contenido en tu formato preferido. Para URLs, deben ser de acceso público y apuntar a un archivo PDF válido. Nota: No se admiten enlaces de Google Drive, Dropbox y otros almacenamientos en la nube; utiliza en su lugar una URL de descarga directa desde un servidor web.

## Herramientas

### `mistral_parser`

Analizar documentos PDF utilizando la API de OCR de Mistral

#### Entrada

| Parámetro | Tipo | Obligatorio | Descripción |
| --------- | ---- | ----------- | ----------- |
| `filePath` | string | Sí | URL a un documento PDF para ser procesado |
| `fileUpload` | object | No | Datos de carga de archivo desde el componente de carga de archivos |
| `resultType` | string | No | Tipo de resultado analizado \(markdown, texto o json\). Por defecto es markdown. |
| `includeImageBase64` | boolean | No | Incluir imágenes codificadas en base64 en la respuesta |
| `pages` | array | No | Páginas específicas para procesar \(array de números de página, comenzando desde 0\) |
| `imageLimit` | number | No | Número máximo de imágenes para extraer del PDF |
| `imageMinSize` | number | No | Altura y anchura mínimas de las imágenes para extraer del PDF |
| `apiKey` | string | Sí | Clave API de Mistral \(MISTRAL_API_KEY\) |

#### Salida

| Parámetro | Tipo | Descripción |
| --------- | ---- | ----------- |
| `success` | boolean | Indica si el PDF se analizó correctamente |
| `content` | string | Contenido extraído en el formato solicitado \(markdown, texto o JSON\) |
| `metadata` | object | Metadatos de procesamiento que incluyen jobId, fileType, pageCount e información de uso |

## Notas

- Categoría: `tools`
- Tipo: `mistral_parse`
