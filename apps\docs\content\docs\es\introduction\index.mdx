---
title: Introducción
---

import { Card, Cards } from 'fumadocs-ui/components/card'
import { Callout } from 'fumadocs-ui/components/callout'
import { Image } from '@/components/ui/image'

Sim es un constructor visual de flujos de trabajo para aplicaciones de IA que te permite crear flujos de trabajo de agentes de IA de forma visual. Crea agentes de IA potentes, flujos de trabajo de automatización y pipelines de procesamiento de datos conectando bloques en un lienzo—sin necesidad de código.

<div className="flex justify-center">
  <Image
    src="/static/introduction.png"
    alt="Lienzo visual de flujos de trabajo de Sim"
    width={700}
    height={450}
    className="my-6"
  />
</div>

## Lo Que Puedes Crear

**Asistentes de IA y Chatbots**
Crea agentes inteligentes que pueden buscar en la web, acceder a tu calendario, enviar correos electrónicos e interactuar con tus herramientas de negocio.

**Automatización de Procesos de Negocio**
Automatiza tareas repetitivas como entrada de datos, generación de informes, respuestas de soporte al cliente y creación de contenido.

**Procesamiento y Análisis de Datos**
Extrae información de documentos, analiza conjuntos de datos, genera informes y sincroniza datos entre sistemas.

**Flujos de Trabajo de Integración de API**
Conecta múltiples servicios en endpoints unificados, orquesta lógica de negocio compleja y maneja automatización basada en eventos.

## Cómo Funciona

**Lienzo Visual**
Arrastra y suelta bloques para crear flujos de trabajo. Conecta modelos de IA, bases de datos, APIs y herramientas de negocio con conexiones simples de apuntar y hacer clic.

**Bloques Inteligentes**
Elige entre bloques de procesamiento (agentes de IA, APIs, funciones), bloques de lógica (condiciones, bucles, enrutadores) y bloques de salida (respuestas, evaluadores).

**Múltiples Disparadores**
Inicia flujos de trabajo a través de interfaz de chat, API REST, webhooks, trabajos programados o eventos externos de servicios como Slack y GitHub.

**Colaboración en Equipo**
Trabaja simultáneamente con miembros del equipo en el mismo flujo de trabajo con edición en tiempo real y gestión de permisos.

## Próximos Pasos

¿Listo para crear tu primer flujo de trabajo de IA?

<Cards>
  <Card title="Primeros Pasos" href="/getting-started">
    Crea tu primer flujo de trabajo en 10 minutos
  </Card>
  <Card title="Bloques de Flujo de Trabajo" href="/blocks">
    Aprende sobre los bloques de construcción
  </Card>
  <Card title="Herramientas e Integraciones" href="/tools">
    Explora más de 60 integraciones integradas
  </Card>
  <Card title="Permisos de Equipo" href="/permissions/roles-and-permissions">
    Configura roles y permisos del espacio de trabajo
  </Card>
</Cards>

¿Necesitas algo personalizado? Usa nuestra [integración MCP](/mcp) para conectar cualquier servicio externo.

## Opciones de implementación

**Alojado en la nube**: Comienza al instante en [sim.ai](https://sim.ai) con infraestructura gestionada, escalado automático y monitorización integrada.

**Autoalojado**: Implementa en tu propia infraestructura usando Docker, con soporte para modelos de IA locales a través de Ollama para una privacidad completa de datos.

## Próximos pasos

¿Listo para construir tu primer flujo de trabajo de IA?

<Cards>
  <Card title="Primeros pasos" href="/getting-started">
    Crea tu primer flujo de trabajo en 10 minutos
  </Card>
  <Card title="Bloques de flujo de trabajo" href="/blocks">
    Aprende sobre los componentes básicos
  </Card>
  <Card title="Herramientas e integraciones" href="/tools">
    Explora más de 60 integraciones incorporadas
  </Card>
  <Card title="Permisos de equipo" href="/permissions/roles-and-permissions">
    Configura roles y permisos del espacio de trabajo
  </Card>
</Cards>