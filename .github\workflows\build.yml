name: Build and Publish Docker Image

on:
  push:
    branches: [main, staging]

jobs:
  build-and-push:
    strategy:
      fail-fast: false
      matrix:
        include:
          # AMD64 builds on x86 runners
          - dockerfile: ./docker/app.Dockerfile
            image: ghcr.io/simstudioai/simstudio
            platform: linux/amd64
            arch: amd64
            runner: linux-x64-8-core
          - dockerfile: ./docker/db.Dockerfile
            image: ghcr.io/simstudioai/migrations
            platform: linux/amd64
            arch: amd64
            runner: linux-x64-8-core
          - dockerfile: ./docker/realtime.Dockerfile
            image: ghcr.io/simstudioai/realtime
            platform: linux/amd64
            arch: amd64
            runner: linux-x64-8-core
          # ARM64 builds on native ARM64 runners
          - dockerfile: ./docker/app.Dockerfile
            image: ghcr.io/simstudioai/simstudio
            platform: linux/arm64
            arch: arm64
            runner: linux-arm64-8-core
          - dockerfile: ./docker/db.Dockerfile
            image: ghcr.io/simstudioai/migrations
            platform: linux/arm64
            arch: arm64
            runner: linux-arm64-8-core
          - dockerfile: ./docker/realtime.Dockerfile
            image: ghcr.io/simstudioai/realtime
            platform: linux/arm64
            arch: arm64
            runner: linux-arm64-8-core
    runs-on: ${{ matrix.runner }}
    permissions:
      contents: read
      packages: write

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Log in to the Container registry
        if: github.event_name != 'pull_request' && github.ref == 'refs/heads/main'
        uses: docker/login-action@v3
        with:
          registry: ghcr.io
          username: ${{ github.repository_owner }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Extract metadata (tags, labels) for Docker
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ matrix.image }}
          tags: |
            type=raw,value=latest-${{ matrix.arch }},enable=${{ github.ref == 'refs/heads/main' }}
            type=raw,value=staging-${{ github.sha }}-${{ matrix.arch }},enable=${{ github.ref == 'refs/heads/staging' }}
            type=sha,format=long,suffix=-${{ matrix.arch }}

      - name: Build and push Docker image
        uses: docker/build-push-action@v6
        with:
          context: .
          file: ${{ matrix.dockerfile }}
          platforms: ${{ matrix.platform }}
          push: ${{ github.event_name != 'pull_request' && github.ref == 'refs/heads/main' }}
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=gha,scope=build-v3
          cache-to: type=gha,mode=max,scope=build-v3
          provenance: false
          sbom: false

  create-manifests:
    runs-on: ubuntu-latest
    needs: build-and-push
    if: github.event_name != 'pull_request' && github.ref == 'refs/heads/main'
    strategy:
      matrix:
        include:
          - image: ghcr.io/simstudioai/simstudio
          - image: ghcr.io/simstudioai/migrations
          - image: ghcr.io/simstudioai/realtime
    permissions:
      contents: read
      packages: write

    steps:
      - name: Log in to the Container registry
        uses: docker/login-action@v3
        with:
          registry: ghcr.io
          username: ${{ github.repository_owner }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Extract metadata for manifest
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ matrix.image }}
          tags: |
            type=raw,value=latest,enable=${{ github.ref == 'refs/heads/main' }}
            type=sha,format=long

      - name: Create and push manifest
        run: |
          # Extract the tags from metadata (these are the final manifest tags we want)
          MANIFEST_TAGS="${{ steps.meta.outputs.tags }}"

          # Create manifest for each tag
          for manifest_tag in $MANIFEST_TAGS; do
            echo "Creating manifest for $manifest_tag"

            # The architecture-specific images have -amd64 and -arm64 suffixes
            amd64_image="${manifest_tag}-amd64"
            arm64_image="${manifest_tag}-arm64"

            echo "Looking for images: $amd64_image and $arm64_image"

            # Check if both architecture images exist
            if docker manifest inspect "$amd64_image" >/dev/null 2>&1 && docker manifest inspect "$arm64_image" >/dev/null 2>&1; then
              echo "Both images found, creating manifest..."
              docker manifest create "$manifest_tag" \
                "$amd64_image" \
                "$arm64_image"
              docker manifest push "$manifest_tag"
              echo "Successfully created and pushed manifest for $manifest_tag"
            else
              echo "Error: One or both architecture images not found"
              echo "Checking AMD64 image: $amd64_image"
              docker manifest inspect "$amd64_image" || echo "AMD64 image not found"
              echo "Checking ARM64 image: $arm64_image"
              docker manifest inspect "$arm64_image" || echo "ARM64 image not found"
              exit 1
            fi
          done