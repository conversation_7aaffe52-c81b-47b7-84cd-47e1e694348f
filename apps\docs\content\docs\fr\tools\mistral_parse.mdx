---
title: Mistral Parser
description: Extraire du texte à partir de documents PDF
---

import { BlockInfoCard } from "@/components/ui/block-info-card"

<BlockInfoCard 
  type="mistral_parse"
  color="#000000"
  icon={true}
  iconSvg={`<svg className="block-icon"
      
      
      
      viewBox='1 0.5 24 22'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
      preserveAspectRatio='xMidYMid meet'
    >
      <g clipPath='url(#clip0_1621_58)'>
        <path d='M17.4541 0H21.8177V4.39481H17.4541V0Z' fill='black' />
        <path d='M19.6367 0H24.0003V4.39481H19.6367V0Z' fill='#F7D046' />
        <path
          d='M0 0H4.36359V4.39481H0V0ZM0 4.39481H4.36359V8.78961H0V4.39481ZM0 8.78971H4.36359V13.1845H0V8.78971ZM0 13.1845H4.36359V17.5793H0V13.1845ZM0 17.5794H4.36359V21.9742H0V17.5794Z'
          fill='black'
        />
        <path d='M2.18164 0H6.54523V4.39481H2.18164V0Z' fill='#F7D046' />
        <path
          d='M19.6362 4.39478H23.9998V8.78958H19.6362V4.39478ZM2.18164 4.39478H6.54523V8.78958H2.18164V4.39478Z'
          fill='#F2A73B'
        />
        <path d='M13.0908 4.39478H17.4544V8.78958H13.0908V4.39478Z' fill='black' />
        <path
          d='M15.2732 4.39478H19.6368V8.78958H15.2732V4.39478ZM6.5459 4.39478H10.9095V8.78958H6.5459V4.39478Z'
          fill='#F2A73B'
        />
        <path
          d='M10.9096 8.78979H15.2732V13.1846H10.9096V8.78979ZM15.2732 8.78979H19.6368V13.1846H15.2732V8.78979ZM6.5459 8.78979H10.9096V13.1846H6.5459V8.78979Z'
          fill='#EE792F'
        />
        <path d='M8.72754 13.1846H13.0911V17.5794H8.72754V13.1846Z' fill='black' />
        <path d='M10.9092 13.1846H15.2728V17.5794H10.9092V13.1846Z' fill='#EB5829' />
        <path
          d='M19.6362 8.78979H23.9998V13.1846H19.6362V8.78979ZM2.18164 8.78979H6.54523V13.1846H2.18164V8.78979Z'
          fill='#EE792F'
        />
        <path d='M17.4541 13.1846H21.8177V17.5794H17.4541V13.1846Z' fill='black' />
        <path d='M19.6367 13.1846H24.0003V17.5794H19.6367V13.1846Z' fill='#EB5829' />
        <path d='M17.4541 17.5793H21.8177V21.9742H17.4541V17.5793Z' fill='black' />
        <path d='M2.18164 13.1846H6.54523V17.5794H2.18164V13.1846Z' fill='#EB5829' />
        <path
          d='M19.6362 17.5793H23.9998V21.9742H19.6362V17.5793ZM2.18164 17.5793H6.54523V21.9742H2.18164V17.5793Z'
          fill='#EA3326'
        />
      </g>
      <defs>
        <clipPath id='clip0_1621_58'>
          <rect   fill='white' />
        </clipPath>
      </defs>
    </svg>`}
/>

{/* MANUAL-CONTENT-START:intro */}
L'outil Mistral Parse offre un moyen puissant d'extraire et de traiter le contenu des documents PDF en utilisant [l'API OCR de Mistral](https://mistral.ai/). Cet outil exploite la reconnaissance optique de caractères avancée pour extraire avec précision le texte et la structure des fichiers PDF, facilitant ainsi l'intégration des données documentaires dans vos flux de travail d'agents.

Avec l'outil Mistral Parse, vous pouvez :

- **Extraire du texte des PDF** : convertir avec précision le contenu PDF en formats texte, markdown ou JSON
- **Traiter les PDF à partir d'URL** : extraire directement le contenu des PDF hébergés en ligne en fournissant leurs URL
- **Conserver la structure du document** : préserver la mise en forme, les tableaux et la disposition des PDF originaux
- **Extraire des images** : inclure optionnellement les images intégrées dans les PDF
- **Sélectionner des pages spécifiques** : traiter uniquement les pages dont vous avez besoin dans les documents multi-pages

L'outil Mistral Parse est particulièrement utile dans les scénarios où vos agents doivent travailler avec du contenu PDF, comme l'analyse de rapports, l'extraction de données de formulaires ou le traitement de texte à partir de documents numérisés. Il simplifie le processus de mise à disposition du contenu PDF pour vos agents, leur permettant de travailler avec les informations stockées dans les PDF aussi facilement qu'avec une saisie de texte directe.
{/* MANUAL-CONTENT-END */}

## Instructions d'utilisation

Extrayez le texte et la structure des documents PDF en utilisant l'API OCR de Mistral. Saisissez une URL vers un document PDF ou téléchargez directement un fichier PDF. Configurez les options de traitement et obtenez le contenu dans votre format préféré. Pour les URL, elles doivent être accessibles publiquement et pointer vers un fichier PDF valide. Remarque : les liens Google Drive, Dropbox et autres services de stockage cloud ne sont pas pris en charge ; utilisez plutôt une URL de téléchargement direct depuis un serveur web.

## Outils

### `mistral_parser`

Analyser des documents PDF avec l'API OCR de Mistral

#### Entrée

| Paramètre | Type | Obligatoire | Description |
| --------- | ---- | ---------- | ----------- |
| `filePath` | chaîne | Oui | URL vers un document PDF à traiter |
| `fileUpload` | objet | Non | Données de téléchargement de fichier provenant du composant de téléchargement de fichier |
| `resultType` | chaîne | Non | Type de résultat analysé \(markdown, texte ou json\). Par défaut : markdown. |
| `includeImageBase64` | booléen | Non | Inclure les images encodées en base64 dans la réponse |
| `pages` | tableau | Non | Pages spécifiques à traiter \(tableau de numéros de page, commençant par 0\) |
| `imageLimit` | nombre | Non | Nombre maximum d'images à extraire du PDF |
| `imageMinSize` | nombre | Non | Hauteur et largeur minimales des images à extraire du PDF |
| `apiKey` | chaîne | Oui | Clé API Mistral \(MISTRAL_API_KEY\) |

#### Sortie

| Paramètre | Type | Description |
| --------- | ---- | ----------- |
| `success` | boolean | Indique si le PDF a été analysé avec succès |
| `content` | string | Contenu extrait dans le format demandé (markdown, texte ou JSON) |
| `metadata` | object | Métadonnées de traitement incluant jobId, fileType, pageCount et informations d'utilisation |

## Remarques

- Catégorie : `tools`
- Type : `mistral_parse`
