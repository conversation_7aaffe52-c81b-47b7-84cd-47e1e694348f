---
title: Microsoft Planner
description: <PERSON> y crea tareas en Microsoft Planner
---

import { BlockInfoCard } from "@/components/ui/block-info-card"

<BlockInfoCard 
  type="microsoft_planner"
  color="#E0E0E0"
  icon={true}
  iconSvg={`<svg className="block-icon"  fill='currentColor' viewBox='-1 -1 27 27' xmlns='http://www.w3.org/2000/svg'>
      <defs>
        <linearGradient
          id='paint0_linear_3984_11038'
          x1='6.38724'
          y1='3.74167'
          x2='2.15779'
          y2='12.777'
          gradientUnits='userSpaceOnUse'
        >
          <stop stopColor='#8752E0' />
          <stop offset='1' stopColor='#541278' />
        </linearGradient>
        <linearGradient
          id='paint1_linear_3984_11038'
          x1='8.38032'
          y1='11.0696'
          x2='4.94062'
          y2='7.69244'
          gradientUnits='userSpaceOnUse'
        >
          <stop offset='0.12172' stopColor='#3D0D59' />
          <stop offset='1' stopColor='#7034B0' stopOpacity='0' />
        </linearGradient>
        <linearGradient
          id='paint2_linear_3984_11038'
          x1='18.3701'
          y1='-3.33385e-05'
          x2='9.85717'
          y2='20.4192'
          gradientUnits='userSpaceOnUse'
        >
          <stop stopColor='#DB45E0' />
          <stop offset='1' stopColor='#6C0F71' />
        </linearGradient>
        <linearGradient
          id='paint3_linear_3984_11038'
          x1='18.3701'
          y1='-3.33385e-05'
          x2='9.85717'
          y2='20.4192'
          gradientUnits='userSpaceOnUse'
        >
          <stop stopColor='#DB45E0' />
          <stop offset='0.677403' stopColor='#A829AE' />
          <stop offset='1' stopColor='#8F28B3' />
        </linearGradient>
        <linearGradient
          id='paint4_linear_3984_11038'
          x1='18.0002'
          y1='7.49958'
          x2='14.0004'
          y2='23.9988'
          gradientUnits='userSpaceOnUse'
        >
          <stop stopColor='#3DCBFF' />
          <stop offset='1' stopColor='#00479E' />
        </linearGradient>
        <linearGradient
          id='paint5_linear_3984_11038'
          x1='18.2164'
          y1='7.92626'
          x2='10.5237'
          y2='22.9363'
          gradientUnits='userSpaceOnUse'
        >
          <stop stopColor='#3DCBFF' />
          <stop offset='1' stopColor='#4A40D4' />
        </linearGradient>
      </defs>
      <path
        d='M8.25809 15.7412C7.22488 16.7744 5.54971 16.7744 4.5165 15.7412L0.774909 11.9996C-0.258303 10.9664 -0.258303 9.29129 0.774908 8.25809L4.5165 4.51655C5.54971 3.48335 7.22488 3.48335 8.25809 4.51655L11.9997 8.2581C13.0329 9.29129 13.0329 10.9664 11.9997 11.9996L8.25809 15.7412Z'
        fill='url(#paint0_linear_3984_11038)'
      />
      <path
        d='M8.25809 15.7412C7.22488 16.7744 5.54971 16.7744 4.5165 15.7412L0.774909 11.9996C-0.258303 10.9664 -0.258303 9.29129 0.774908 8.25809L4.5165 4.51655C5.54971 3.48335 7.22488 3.48335 8.25809 4.51655L11.9997 8.2581C13.0329 9.29129 13.0329 10.9664 11.9997 11.9996L8.25809 15.7412Z'
        fill='url(#paint1_linear_3984_11038)'
      />
      <path
        d='M0.774857 11.9999C1.80809 13.0331 3.48331 13.0331 4.51655 11.9999L15.7417 0.774926C16.7749 -0.258304 18.4501 -0.258309 19.4834 0.774914L23.225 4.51655C24.2583 5.54977 24.2583 7.22496 23.225 8.25819L11.9999 19.4832C10.9667 20.5164 9.29146 20.5164 8.25822 19.4832L0.774857 11.9999Z'
        fill='url(#paint2_linear_3984_11038)'
      />
      <path
        d='M0.774857 11.9999C1.80809 13.0331 3.48331 13.0331 4.51655 11.9999L15.7417 0.774926C16.7749 -0.258304 18.4501 -0.258309 19.4834 0.774914L23.225 4.51655C24.2583 5.54977 24.2583 7.22496 23.225 8.25819L11.9999 19.4832C10.9667 20.5164 9.29146 20.5164 8.25822 19.4832L0.774857 11.9999Z'
        fill='url(#paint3_linear_3984_11038)'
      />
      <path
        d='M4.51642 15.7413C5.54966 16.7746 7.22487 16.7746 8.25812 15.7413L15.7415 8.25803C16.7748 7.2248 18.45 7.2248 19.4832 8.25803L23.2249 11.9997C24.2582 13.0329 24.2582 14.7081 23.2249 15.7413L15.7415 23.2246C14.7083 24.2579 13.033 24.2579 11.9998 23.2246L4.51642 15.7413Z'
        fill='url(#paint4_linear_3984_11038)'
      />
      <path
        d='M4.51642 15.7413C5.54966 16.7746 7.22487 16.7746 8.25812 15.7413L15.7415 8.25803C16.7748 7.2248 18.45 7.2248 19.4832 8.25803L23.2249 11.9997C24.2582 13.0329 24.2582 14.7081 23.2249 15.7413L15.7415 23.2246C14.7083 24.2579 13.033 24.2579 11.9998 23.2246L4.51642 15.7413Z'
        fill='url(#paint5_linear_3984_11038)'
      />
    </svg>`}
/>

{/* MANUAL-CONTENT-START:intro */}
[Microsoft Planner](https://www.microsoft.com/en-us/microsoft-365/planner) es una herramienta de gestión de tareas que ayuda a los equipos a organizar el trabajo visualmente mediante tableros, tareas y contenedores. Integrado con Microsoft 365, ofrece una forma sencilla e intuitiva de gestionar proyectos en equipo, asignar responsabilidades y seguir el progreso.

Con Microsoft Planner, puedes:

- **Crear y gestionar tareas**: Añadir nuevas tareas con fechas de vencimiento, prioridades y usuarios asignados
- **Organizar con contenedores**: Agrupar tareas por fase, estado o categoría para reflejar el flujo de trabajo de tu equipo
- **Visualizar el estado del proyecto**: Usar tableros, gráficos y filtros para monitorear la carga de trabajo y seguir el progreso
- **Mantener la integración con Microsoft 365**: Conectar sin problemas las tareas con Teams, Outlook y otras herramientas de Microsoft

En Sim, la integración de Microsoft Planner permite a tus agentes crear, leer y gestionar tareas de forma programática como parte de sus flujos de trabajo. Los agentes pueden generar nuevas tareas basadas en solicitudes entrantes, recuperar detalles de tareas para tomar decisiones y seguir el estado de los proyectos, todo sin intervención humana. Ya sea que estés creando flujos de trabajo para la incorporación de clientes, seguimiento de proyectos internos o generación de tareas de seguimiento, la integración de Microsoft Planner con Sim proporciona a tus agentes una forma estructurada de coordinar el trabajo, automatizar la creación de tareas y mantener a los equipos alineados.
{/* MANUAL-CONTENT-END */}

## Instrucciones de uso

Integra la funcionalidad de Microsoft Planner para gestionar tareas. Lee todas las tareas del usuario, tareas de planes específicos, tareas individuales o crea nuevas tareas con varias propiedades como título, descripción, fecha de vencimiento y asignados utilizando autenticación OAuth.

## Herramientas

### `microsoft_planner_read_task`

Leer tareas de Microsoft Planner - obtener todas las tareas del usuario o todas las tareas de un plan específico

#### Entrada

| Parámetro | Tipo | Obligatorio | Descripción |
| --------- | ---- | -------- | ----------- |
| `planId` | string | No | El ID del plan del que obtener tareas (si no se proporciona, obtiene todas las tareas del usuario) |
| `taskId` | string | No | El ID de la tarea a obtener |

#### Salida

| Parámetro | Tipo | Descripción |
| --------- | ---- | ----------- |
| `success` | boolean | Indica si las tareas se recuperaron correctamente |
| `tasks` | array | Array de objetos de tarea con propiedades filtradas |
| `metadata` | object | Metadatos que incluyen planId, userId y planUrl |

### `microsoft_planner_create_task`

Crear una nueva tarea en Microsoft Planner

#### Entrada

| Parámetro | Tipo | Obligatorio | Descripción |
| --------- | ---- | -------- | ----------- |
| `planId` | string | Sí | El ID del plan donde se creará la tarea |
| `title` | string | Sí | El título de la tarea |
| `description` | string | No | La descripción de la tarea |
| `dueDateTime` | string | No | La fecha y hora de vencimiento para la tarea (formato ISO 8601) |
| `assigneeUserId` | string | No | El ID del usuario al que asignar la tarea |
| `bucketId` | string | No | El ID del bucket donde colocar la tarea |

#### Salida

| Parámetro | Tipo | Descripción |
| --------- | ---- | ----------- |
| `success` | boolean | Indica si la tarea se creó correctamente |
| `task` | object | El objeto de tarea creado con todas sus propiedades |
| `metadata` | object | Metadatos que incluyen planId, taskId y taskUrl |

## Notas

- Categoría: `tools`
- Tipo: `microsoft_planner`
