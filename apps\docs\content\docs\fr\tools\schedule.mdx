---
title: Planification
description: Déclencher l'exécution du workflow selon un calendrier
---

import { BlockInfoCard } from "@/components/ui/block-info-card"

<BlockInfoCard 
  type="schedule"
  color="#7B68EE"
  icon={true}
  iconSvg={`<svg className="block-icon"
      
      xmlns='http://www.w3.org/2000/svg'
      
      
      viewBox='0 0 24 24'
      fill='none'
      stroke='currentColor'
      strokeWidth='2'
      strokeLinecap='round'
      strokeLinejoin='round'
    >
      <path d='M8 2v4' />
      <path d='M16 2v4' />
      <rect   x='3' y='4' rx='2' />
      <path d='M3 10h18' />
    </svg>`}
/>

## Instructions d'utilisation

Configurez l'exécution automatisée des workflows avec des options de temporisation flexibles. Mettez en place des workflows récurrents qui s'exécutent à des intervalles ou moments spécifiques.

## Remarques

- Catégorie : `triggers`
- Type : `schedule`
