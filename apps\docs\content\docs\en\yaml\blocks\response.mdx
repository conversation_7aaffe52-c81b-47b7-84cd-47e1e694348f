---
title: Response Block YAML Schema
description: YAML configuration reference for Response blocks
---

## Schema Definition

```yaml
type: object
required:
  - type
  - name
properties:
  type:
    type: string
    enum: [response]
    description: Block type identifier
  name:
    type: string
    description: Display name for this response block
  inputs:
    type: object
    properties:
      dataMode:
        type: string
        enum: [structured, json]
        description: Mode for defining response data structure
        default: structured
      builderData:
        type: object
        description: Structured response data (when dataMode is 'structured')
      data:
        type: object
        description: JSON response data (when dataMode is 'json')
      status:
        type: number
        description: HTTP status code
        default: 200
        minimum: 100
        maximum: 599
      headers:
        type: array
        description: Response headers as table entries
        items:
          type: object
          properties:
            id:
              type: string
              description: Unique identifier for the header entry
            key:
              type: string
              description: Header name
            value:
              type: string
              description: Header value
            cells:
              type: object
              description: Cell display values for the table interface
              properties:
                Key:
                  type: string
                  description: Display value for the key column
                Value:
                  type: string
                  description: Display value for the value column
```

## Connection Configuration

Response blocks are terminal blocks (no outgoing connections) and define the final output:

```yaml
# No connections object needed - Response blocks are always terminal
```

## Examples

### Simple Response

```yaml
simple-response:
  type: response
  name: "Simple Response"
  inputs:
    data:
      message: "Hello World"
      timestamp: <function.timestamp>
    status: 200
```

### Success Response

```yaml
success-response:
  type: response
  name: "Success Response"
  inputs:
    data:
      success: true
      user:
        id: <agent.user_id>
        name: <agent.user_name>
        email: <agent.user_email>
      created_at: <function.timestamp>
    status: 201
    headers:
      - key: "Location"
        value: "/api/users/<agent.user_id>"
      - key: "X-Created-By"
        value: "workflow-engine"
```

### Response with Complete Table Header Format

When headers are created through the UI table interface, the YAML includes additional metadata:

```yaml
api-response:
  type: response
  name: "API Response"
  inputs:
    data:
      message: "Request processed successfully"
      id: <agent.request_id>
    status: 200
    headers:
      - id: header-1-uuid-here
        key: "Content-Type"
        value: "application/json"
        cells:
          Key: "Content-Type"
          Value: "application/json"
      - id: header-2-uuid-here
        key: "Cache-Control"
        value: "no-cache"
        cells:
          Key: "Cache-Control"
          Value: "no-cache"
      - id: header-3-uuid-here
        key: "X-API-Version"
        value: "2.1"
        cells:
          Key: "X-API-Version"
          Value: "2.1"
```

### Error Response

```yaml
error-response:
  type: response
  name: "Error Response"
  inputs:
    data:
      error: true
      message: <agent.error_message>
      code: "VALIDATION_FAILED"
      details: <function.validation_errors>
    status: 400
    headers:
      - key: "X-Error-Code"
        value: "VALIDATION_FAILED"
```

### Paginated Response

```yaml
paginated-response:
  type: response
  name: "Paginated Response"
  inputs:
    data:
      data: <agent.results>
      pagination:
        page: <start.page>
        per_page: <start.per_page>
        total: <function.total_count>
        total_pages: <function.total_pages>
    status: 200
    headers:
      - key: "X-Total-Count"
        value: <function.total_count>
      - key: "Cache-Control"
        value: "public, max-age=300"
      - key: "Content-Type"
        value: "application/json"
```

## Table Parameter Formats

The Response block supports two formats for headers:

### Simplified Format (Manual YAML)

When writing YAML manually, you can use the simplified format:

```yaml
headers:
  - key: "Content-Type"
    value: "application/json"
  - key: "Cache-Control"
    value: "no-cache"
```

### Complete Table Format (UI Generated)

When headers are created through the UI table interface, the YAML includes additional metadata:

```yaml
headers:
  - id: unique-identifier-here
    key: "Content-Type"
    value: "application/json"
    cells:
      Key: "Content-Type"
      Value: "application/json"
```

**Key Differences:**
- `id`: Unique identifier for tracking the table row
- `cells`: Display values used by the UI table interface
- Both formats are functionally equivalent for workflow execution
- The complete format preserves UI state when importing/exporting workflows

**Important:** Always quote header names and values that contain special characters:

```yaml
headers:
  - id: content-type-uuid
    cells:
      Key: "Content-Type"
      Value: "application/json"
  - id: cache-control-uuid
    cells:
      Key: "Cache-Control" 
      Value: "no-cache"
```
``` 