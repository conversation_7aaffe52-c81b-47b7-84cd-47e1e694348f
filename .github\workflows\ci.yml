name: CI

on:
  push:
    branches: [main, staging]
  pull_request:
    branches: [main, staging]

jobs:
  test:
    name: Test and Build
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Bun
        uses: oven-sh/setup-bun@v2
        with:
          bun-version: latest

      - name: Setup Node
        uses: actions/setup-node@v4
        with:
          node-version: latest

      - name: Install dependencies
        run: bun install --frozen-lockfile

      - name: Run tests with coverage
        env:
          NODE_OPTIONS: '--no-warnings'
          NEXT_PUBLIC_APP_URL: 'https://www.sim.ai'
          ENCRYPTION_KEY: '7cf672e460e430c1fba707575c2b0e2ad5a99dddf9b7b7e3b5646e630861db1c' # dummy key for CI only
        run: bun run test

      - name: Build application
        env:
          NODE_OPTIONS: '--no-warnings'
          NEXT_PUBLIC_APP_URL: 'https://www.sim.ai'
          STRIPE_SECRET_KEY: 'dummy_key_for_ci_only'
          STRIPE_WEBHOOK_SECRET: 'dummy_secret_for_ci_only'
          RESEND_API_KEY: 'dummy_key_for_ci_only'
          AWS_REGION: 'us-west-2'
          ENCRYPTION_KEY: '7cf672e460e430c1fba707575c2b0e2ad5a99dddf9b7b7e3b5646e630861db1c' # dummy key for CI only
        run: bun run build

      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v5
        with:
          directory: ./apps/sim/coverage
          fail_ci_if_error: false
          verbose: true

  migrations:
    name: Apply Database Migrations
    runs-on: ubuntu-latest
    if: github.event_name == 'push' && (github.ref == 'refs/heads/main' || github.ref == 'refs/heads/staging')
    needs: test
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Bun
        uses: oven-sh/setup-bun@v2
        with:
          bun-version: latest

      - name: Install dependencies
        run: bun install

      - name: Apply migrations
        working-directory: ./apps/sim
        env:
          DATABASE_URL: ${{ github.ref == 'refs/heads/main' && secrets.DATABASE_URL || secrets.STAGING_DATABASE_URL }}
        run: bunx drizzle-kit migrate
