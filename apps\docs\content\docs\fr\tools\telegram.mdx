---
title: Telegram
description: Envoyez des messages via Telegram ou déclenchez des workflows à
  partir d'événements Telegram
---

import { BlockInfoCard } from "@/components/ui/block-info-card"

<BlockInfoCard 
  type="telegram"
  color="#E0E0E0"
  icon={true}
  iconSvg={`<svg className="block-icon"
      
      xmlns='http://www.w3.org/2000/svg'
      viewBox='0 0 24 24'
      
      
      fill='none'
    >
      <circle cx='12' cy='12' r='10' fill='#0088CC' />
      <path
        d='M16.7 8.4c.1-.6-.4-1.1-1-.8l-9.8 4.3c-.4.2-.4.8.1.9l2.1.7c.******* 1.1-.2l4.5-3.1c.1-.*******.2l-3.2 3.5c-.3.3-.2.8.2 1l3.6 2.3c.4.2.9-.1 1-.5l1.2-7.8Z'
        fill='white'
      />
    </svg>`}
/>

{/* MANUAL-CONTENT-START:intro */}
[Telegram](https://telegram.org) est une plateforme de messagerie sécurisée basée sur le cloud qui permet une communication rapide et fiable sur tous les appareils et plateformes. Avec plus de 700 millions d'utilisateurs actifs mensuels, Telegram s'est imposé comme l'un des services de messagerie leaders mondiaux, reconnu pour sa sécurité, sa rapidité et ses puissantes capacités d'API.

L'API Bot de Telegram fournit un cadre robuste pour créer des solutions de messagerie automatisées et intégrer des fonctionnalités de communication dans les applications. Avec la prise en charge des médias enrichis, des claviers intégrés et des commandes personnalisées, les bots Telegram peuvent faciliter des modèles d'interaction sophistiqués et des workflows automatisés.

Découvrez comment créer un déclencheur webhook dans Sim qui lance harmonieusement des workflows à partir de messages Telegram. Ce tutoriel vous guide à travers la configuration d'un webhook, sa configuration avec l'API bot de Telegram, et le déclenchement d'actions automatisées en temps réel. Parfait pour rationaliser les tâches directement depuis votre chat !

<iframe
  width="100%"
  height="400"
  src="https://www.youtube.com/embed/9oKcJtQ0_IM"
  title="Utiliser Telegram avec Sim"
  frameBorder="0"
  allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
  allowFullScreen
></iframe>

Apprenez à utiliser l'outil Telegram dans Sim pour automatiser en toute simplicité la livraison de messages à n'importe quel groupe Telegram. Ce tutoriel vous guide à travers l'intégration de l'outil dans votre workflow, la configuration de la messagerie de groupe et le déclenchement de mises à jour automatisées en temps réel. Parfait pour améliorer la communication directement depuis votre espace de travail !

<iframe
  width="100%"
  height="400"
  src="https://www.youtube.com/embed/AG55LpUreGI"
  title="Utiliser Telegram avec Sim"
  frameBorder="0"
  allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
  allowFullScreen
></iframe>

Les fonctionnalités clés de Telegram comprennent :

- Communication sécurisée : Chiffrement de bout en bout et stockage sécurisé dans le cloud pour les messages et les médias
- Plateforme de bots : API de bot puissante pour créer des solutions de messagerie automatisées et des expériences interactives
- Support multimédia riche : Envoi et réception de messages avec mise en forme du texte, images, fichiers et éléments interactifs
- Portée mondiale : Connexion avec des utilisateurs du monde entier avec prise en charge de plusieurs langues et plateformes

Dans Sim, l'intégration de Telegram permet à vos agents d'exploiter ces puissantes fonctionnalités de messagerie dans le cadre de leurs flux de travail. Cela crée des opportunités pour les notifications automatisées, les alertes et les conversations interactives via la plateforme de messagerie sécurisée de Telegram. L'intégration permet aux agents d'envoyer des messages de manière programmatique à des individus ou à des canaux, permettant une communication et des mises à jour opportunes. En connectant Sim à Telegram, vous pouvez créer des agents intelligents qui interagissent avec les utilisateurs via une plateforme de messagerie sécurisée et largement adoptée, parfaite pour délivrer des notifications, des mises à jour et des communications interactives.
{/* MANUAL-CONTENT-END */}

## Instructions d'utilisation

Envoyez des messages à n'importe quel canal Telegram en utilisant votre clé API Bot ou déclenchez des flux de travail à partir des messages de bot Telegram. Intégrez des notifications et des alertes automatisées dans votre flux de travail pour tenir votre équipe informée.

## Outils

### `telegram_message`

Envoyez des messages aux canaux ou utilisateurs Telegram via l'API Bot Telegram. Permet une communication directe et des notifications avec suivi des messages et confirmation de chat.

#### Entrée

| Paramètre | Type | Obligatoire | Description |
| --------- | ---- | ----------- | ----------- |
| `botToken` | chaîne | Oui | Votre jeton d'API Bot Telegram |
| `chatId` | chaîne | Oui | ID du chat Telegram cible |
| `text` | chaîne | Oui | Texte du message à envoyer |

#### Sortie

| Paramètre | Type | Description |
| --------- | ---- | ----------- |
| `success` | boolean | Statut de succès d'envoi du message Telegram |
| `messageId` | number | Identifiant unique du message Telegram |
| `chatId` | string | ID du chat cible où le message a été envoyé |
| `text` | string | Contenu textuel du message envoyé |
| `timestamp` | number | Horodatage Unix lorsque le message a été envoyé |
| `from` | object | Informations sur le bot qui a envoyé le message |

## Notes

- Catégorie : `tools`
- Type : `telegram`
