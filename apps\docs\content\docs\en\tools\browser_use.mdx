---
title: Browser Use
description: Run browser automation tasks
---

import { BlockInfoCard } from "@/components/ui/block-info-card"

<BlockInfoCard 
  type="browser_use"
  color="#E0E0E0"
  icon={true}
  iconSvg={`<svg className="block-icon"
      
      version='1.0'
      xmlns='http://www.w3.org/2000/svg'
      
      
      viewBox='0 0 150 150'
      preserveAspectRatio='xMidYMid meet'
    >
      <g transform='translate(0,150) scale(0.05,-0.05)' fill='#000000' stroke='none'>
        <path
          d='M786 2713 c-184 -61 -353 -217 -439 -405 -76 -165 -65 -539 19 -666
l57 -85 -48 -124 c-203 -517 -79 -930 346 -1155 159 -85 441 -71 585 28 l111
77 196 -76 c763 -293 1353 304 1051 1063 -77 191 -77 189 -14 282 163 239 97
660 -140 893 -235 231 -528 256 -975 83 l-96 -37 -121 67 c-144 79 -383 103
-532 55z m459 -235 c88 -23 96 -51 22 -79 -29 -11 -84 -47 -121 -80 -57 -50
-84 -59 -178 -59 -147 0 -190 -44 -238 -241 -102 -424 -230 -440 -230 -29 1
417 289 606 745 488z m1046 -18 c174 -85 266 -309 239 -582 -26 -256 -165
-165 -230 151 -73 356 -469 332 -954 -58 -587 -472 -829 -1251 -388 -1251 108
0 126 -7 214 -80 54 -44 104 -80 113 -80 54 0 -2 -43 -89 -69 -220 -66 -426
-22 -568 120 -599 599 871 2232 1663 1849z m-234 -510 c969 -1036 357 -1962
-787 -1190 -254 171 -348 303 -323 454 21 128 40 123 231 -59 691 -658 1362
-583 1052 117 -106 239 -366 585 -504 671 l-72 44 98 45 c150 68 169 63 305
-82z m-329 -310 c161 -184 163 -160 -30 -338 -188 -173 -180 -173 -386 19
-163 153 -163 157 7 324 218 213 219 213 409 -5z m354 -375 c92 -239 -179
-462 -377 -309 l-46 35 186 163 c211 186 209 185 237 111z'
        />
      </g>
    </svg>`}
/>

{/* MANUAL-CONTENT-START:intro */}
[BrowserUse](https://browser-use.com/) is a powerful browser automation platform that enables you to create and run browser tasks programmatically. It provides a way to automate web interactions through natural language instructions, allowing you to navigate websites, fill forms, extract data, and perform complex sequences of actions without writing code.

With BrowserUse, you can:

- **Automate web interactions**: Navigate to websites, click buttons, fill forms, and perform other browser actions
- **Extract data**: Scrape content from websites, including text, images, and structured data
- **Execute complex workflows**: Chain multiple actions together to complete sophisticated web tasks
- **Monitor task execution**: Watch browser tasks run in real-time with visual feedback
- **Process results programmatically**: Receive structured output from web automation tasks

In Sim, the BrowserUse integration allows your agents to interact with the web as if they were human users. This enables scenarios like research, data collection, form submission, and web testing - all through simple natural language instructions. Your agents can gather information from websites, interact with web applications, and perform actions that would typically require manual browsing, expanding their capabilities to include the entire web as a resource.
{/* MANUAL-CONTENT-END */}


## Usage Instructions

Execute browser automation tasks with BrowserUse to navigate the web, scrape data, and perform actions as if a real user was interacting with the browser. The task runs asynchronously and the block will poll for completion before returning results.



## Tools

### `browser_use_run_task`

Runs a browser automation task using BrowserUse

#### Input

| Parameter | Type | Required | Description |
| --------- | ---- | -------- | ----------- |
| `task` | string | Yes | What should the browser agent do |
| `variables` | json | No | Optional variables to use as secrets \(format: \{key: value\}\) |
| `format` | string | No | No description |
| `save_browser_data` | boolean | No | Whether to save browser data |
| `model` | string | No | LLM model to use \(default: gpt-4o\) |
| `apiKey` | string | Yes | API key for BrowserUse API |

#### Output

| Parameter | Type | Description |
| --------- | ---- | ----------- |
| `id` | string | Task execution identifier |
| `success` | boolean | Task completion status |
| `output` | json | Task output data |
| `steps` | json | Execution steps taken |



## Notes

- Category: `tools`
- Type: `browser_use`
