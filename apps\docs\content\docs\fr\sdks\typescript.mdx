---
title: TypeScript/JavaScript SDK
---

import { Callout } from 'fumadocs-ui/components/callout'
import { Card, Cards } from 'fumadocs-ui/components/card'
import { Step, Steps } from 'fumadocs-ui/components/steps'
import { Tab, Tabs } from 'fumadocs-ui/components/tabs'

Le SDK officiel TypeScript/JavaScript pour Sim offre une sécurité de type complète et prend en charge les environnements Node.js et navigateur, vous permettant d'exécuter des workflows de manière programmatique depuis vos applications Node.js, applications web et autres environnements JavaScript. Toutes les exécutions de workflow sont actuellement synchrones.

<Callout type="info">
  Le SDK TypeScript offre une sécurité de type complète et prend en charge les environnements Node.js et navigateur. Toutes les exécutions de workflow sont actuellement synchrones.
</Callout>

## Installation

Installez le SDK en utilisant votre gestionnaire de paquets préféré :

<Tabs items={['npm', 'yarn', 'bun']}>
  <Tab value="npm">

    ```bash
    npm install simstudio-ts-sdk
    ```

  </Tab>
  <Tab value="yarn">

    ```bash
    yarn add simstudio-ts-sdk
    ```

  </Tab>
  <Tab value="bun">

    ```bash
    bun add simstudio-ts-sdk
    ```

  </Tab>
</Tabs>

## Démarrage rapide

Voici un exemple simple pour commencer :

```typescript
import { SimStudioClient } from 'simstudio-ts-sdk';

// Initialize the client
const client = new SimStudioClient({
  apiKey: 'your-api-key-here',
  baseUrl: 'https://sim.ai' // optional, defaults to https://sim.ai
});

// Execute a workflow
try {
  const result = await client.executeWorkflow('workflow-id');
  console.log('Workflow executed successfully:', result);
} catch (error) {
  console.error('Workflow execution failed:', error);
}
```

## Référence de l'API

### SimStudioClient

#### Constructeur

```typescript
new SimStudioClient(config: SimStudioConfig)
```

**Configuration :**
- `config.apiKey` (string) : votre clé API Sim
- `config.baseUrl` (string, optionnel) : URL de base pour l'API Sim (par défaut `https://sim.ai`)

#### Méthodes

##### executeWorkflow()

Exécuter un workflow avec des données d'entrée optionnelles.

```typescript
const result = await client.executeWorkflow('workflow-id', {
  input: { message: 'Hello, world!' },
  timeout: 30000 // 30 seconds
});
```

**Paramètres :**
- `workflowId` (string) : L'identifiant du workflow à exécuter
- `options` (ExecutionOptions, facultatif) :
  - `input` (any) : Données d'entrée à transmettre au workflow
  - `timeout` (number) : Délai d'expiration en millisecondes (par défaut : 30000)

**Retourne :** `Promise<WorkflowExecutionResult>`

##### getWorkflowStatus()

Obtenir le statut d'un workflow (statut de déploiement, etc.).

```typescript
const status = await client.getWorkflowStatus('workflow-id');
console.log('Is deployed:', status.isDeployed);
```

**Paramètres :**
- `workflowId` (string) : L'identifiant du workflow

**Retourne :** `Promise<WorkflowStatus>`

##### validateWorkflow()

Valider qu'un workflow est prêt pour l'exécution.

```typescript
const isReady = await client.validateWorkflow('workflow-id');
if (isReady) {
  // Workflow is deployed and ready
}
```

**Paramètres :**
- `workflowId` (string) : L'identifiant du workflow

**Retourne :** `Promise<boolean>`

##### executeWorkflowSync()

<Callout type="info">
  Actuellement, cette méthode est identique à `executeWorkflow()` puisque toutes les exécutions sont synchrones. Cette méthode est fournie pour une compatibilité future lorsque l'exécution asynchrone sera ajoutée.
</Callout>

Exécuter un workflow (actuellement synchrone, identique à `executeWorkflow()`).

```typescript
const result = await client.executeWorkflowSync('workflow-id', {
  input: { data: 'some input' },
  timeout: 60000
});
```

**Paramètres :**
- `workflowId` (string) : L'identifiant du workflow à exécuter
- `options` (ExecutionOptions, facultatif) :
  - `input` (any) : Données d'entrée à transmettre au workflow
  - `timeout` (number) : Délai d'expiration pour la requête initiale en millisecondes

**Retourne :** `Promise<WorkflowExecutionResult>`

##### setApiKey()

Mettre à jour la clé API.

```typescript
client.setApiKey('new-api-key');
```

##### setBaseUrl()

Mettre à jour l'URL de base.

```typescript
client.setBaseUrl('https://my-custom-domain.com');
```

## Types

### WorkflowExecutionResult

```typescript
interface WorkflowExecutionResult {
  success: boolean;
  output?: any;
  error?: string;
  logs?: any[];
  metadata?: {
    duration?: number;
    executionId?: string;
    [key: string]: any;
  };
  traceSpans?: any[];
  totalDuration?: number;
}
```

### WorkflowStatus

```typescript
interface WorkflowStatus {
  isDeployed: boolean;
  deployedAt?: string;
  isPublished: boolean;
  needsRedeployment: boolean;
}
```

### SimStudioError

```typescript
class SimStudioError extends Error {
  code?: string;
  status?: number;
}
```

## Exemples

### Exécution de workflow basique

<Steps>
  <Step title="Initialiser le client">
    Configurez le SimStudioClient avec votre clé API.
  </Step>
  <Step title="Valider le workflow">
    Vérifiez si le workflow est déployé et prêt pour l'exécution.
  </Step>
  <Step title="Exécuter le workflow">
    Lancez le workflow avec vos données d'entrée.
  </Step>
  <Step title="Gérer le résultat">
    Traitez le résultat de l'exécution et gérez les éventuelles erreurs.
  </Step>
</Steps>

```typescript
import { SimStudioClient } from 'simstudio-ts-sdk';

const client = new SimStudioClient({
  apiKey: process.env.SIMSTUDIO_API_KEY!
});

async function runWorkflow() {
  try {
    // Check if workflow is ready
    const isReady = await client.validateWorkflow('my-workflow-id');
    if (!isReady) {
      throw new Error('Workflow is not deployed or ready');
    }

    // Execute the workflow
    const result = await client.executeWorkflow('my-workflow-id', {
      input: {
        message: 'Process this data',
        userId: '12345'
      }
    });

    if (result.success) {
      console.log('Output:', result.output);
      console.log('Duration:', result.metadata?.duration);
    } else {
      console.error('Workflow failed:', result.error);
    }
  } catch (error) {
    console.error('Error:', error);
  }
}

runWorkflow();
```

### Gestion des erreurs

Gérez différents types d'erreurs qui peuvent survenir pendant l'exécution du workflow :

```typescript
import { SimStudioClient, SimStudioError } from 'simstudio-ts-sdk';

const client = new SimStudioClient({
  apiKey: process.env.SIMSTUDIO_API_KEY!
});

async function executeWithErrorHandling() {
  try {
    const result = await client.executeWorkflow('workflow-id');
    return result;
  } catch (error) {
    if (error instanceof SimStudioError) {
      switch (error.code) {
        case 'UNAUTHORIZED':
          console.error('Invalid API key');
          break;
        case 'TIMEOUT':
          console.error('Workflow execution timed out');
          break;
        case 'USAGE_LIMIT_EXCEEDED':
          console.error('Usage limit exceeded');
          break;
        case 'INVALID_JSON':
          console.error('Invalid JSON in request body');
          break;
        default:
          console.error('Workflow error:', error.message);
      }
    } else {
      console.error('Unexpected error:', error);
    }
    throw error;
  }
}
```

### Configuration de l'environnement

Configurez le client en utilisant des variables d'environnement :

<Tabs items={['Development', 'Production']}>
  <Tab value="Development">

    ```typescript
    import { SimStudioClient } from 'simstudio-ts-sdk';

    // Development configuration
    const apiKey = process.env.SIMSTUDIO_API_KEY;
    if (!apiKey) {
      throw new Error('SIMSTUDIO_API_KEY environment variable is required');
    }

    const client = new SimStudioClient({
      apiKey,
      baseUrl: process.env.SIMSTUDIO_BASE_URL // optional
    });
    ```

  </Tab>
  <Tab value="Production">

    ```typescript
    import { SimStudioClient } from 'simstudio-ts-sdk';

    // Production configuration with validation
    const apiKey = process.env.SIMSTUDIO_API_KEY;
    if (!apiKey) {
      throw new Error('SIMSTUDIO_API_KEY environment variable is required');
    }

    const client = new SimStudioClient({
      apiKey,
      baseUrl: process.env.SIMSTUDIO_BASE_URL || 'https://sim.ai'
    });
    ```

  </Tab>
</Tabs>

### Intégration avec Express de Node.js

Intégration avec un serveur Express.js :

```typescript
import express from 'express';
import { SimStudioClient } from 'simstudio-ts-sdk';

const app = express();
const client = new SimStudioClient({
  apiKey: process.env.SIMSTUDIO_API_KEY!
});

app.use(express.json());

app.post('/execute-workflow', async (req, res) => {
  try {
    const { workflowId, input } = req.body;
    
    const result = await client.executeWorkflow(workflowId, {
      input,
      timeout: 60000
    });

    res.json({
      success: true,
      data: result
    });
  } catch (error) {
    console.error('Workflow execution error:', error);
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

app.listen(3000, () => {
  console.log('Server running on port 3000');
});
```

### Route API Next.js

Utilisation avec les routes API Next.js :

```typescript
// pages/api/workflow.ts or app/api/workflow/route.ts
import { NextApiRequest, NextApiResponse } from 'next';
import { SimStudioClient } from 'simstudio-ts-sdk';

const client = new SimStudioClient({
  apiKey: process.env.SIMSTUDIO_API_KEY!
});

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { workflowId, input } = req.body;

    const result = await client.executeWorkflow(workflowId, {
      input,
      timeout: 30000
    });

    res.status(200).json(result);
  } catch (error) {
    console.error('Error executing workflow:', error);
    res.status(500).json({
      error: 'Failed to execute workflow'
    });
  }
}
```

### Utilisation dans le navigateur

Utilisation dans le navigateur (avec une configuration CORS appropriée) :

```typescript
import { SimStudioClient } from 'simstudio-ts-sdk';

// Note: In production, use a proxy server to avoid exposing API keys
const client = new SimStudioClient({
  apiKey: 'your-public-api-key', // Use with caution in browser
  baseUrl: 'https://sim.ai'
});

async function executeClientSideWorkflow() {
  try {
    const result = await client.executeWorkflow('workflow-id', {
      input: {
        userInput: 'Hello from browser'
      }
    });

    console.log('Workflow result:', result);
    
    // Update UI with result
    document.getElementById('result')!.textContent = 
      JSON.stringify(result.output, null, 2);
  } catch (error) {
    console.error('Error:', error);
  }
}

// Attach to button click
document.getElementById('executeBtn')?.addEventListener('click', executeClientSideWorkflow);
```

<Callout type="warning">
  Lors de l'utilisation du SDK dans le navigateur, veillez à ne pas exposer de clés API sensibles. Envisagez d'utiliser un proxy backend ou des clés API publiques avec des permissions limitées.
</Callout>

### Exemple de hook React

Créez un hook React personnalisé pour l'exécution du workflow :

```typescript
import { useState, useCallback } from 'react';
import { SimStudioClient, WorkflowExecutionResult } from 'simstudio-ts-sdk';

const client = new SimStudioClient({
  apiKey: process.env.NEXT_PUBLIC_SIMSTUDIO_API_KEY!
});

interface UseWorkflowResult {
  result: WorkflowExecutionResult | null;
  loading: boolean;
  error: Error | null;
  executeWorkflow: (workflowId: string, input?: any) => Promise<void>;
}

export function useWorkflow(): UseWorkflowResult {
  const [result, setResult] = useState<WorkflowExecutionResult | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  const executeWorkflow = useCallback(async (workflowId: string, input?: any) => {
    setLoading(true);
    setError(null);
    setResult(null);

    try {
      const workflowResult = await client.executeWorkflow(workflowId, {
        input,
        timeout: 30000
      });
      setResult(workflowResult);
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Unknown error'));
    } finally {
      setLoading(false);
    }
  }, []);

  return {
    result,
    loading,
    error,
    executeWorkflow
  };
}

// Usage in component
function WorkflowComponent() {
  const { result, loading, error, executeWorkflow } = useWorkflow();

  const handleExecute = () => {
    executeWorkflow('my-workflow-id', {
      message: 'Hello from React!'
    });
  };

  return (
    <div>
      <button onClick={handleExecute} disabled={loading}>
        {loading ? 'Executing...' : 'Execute Workflow'}
      </button>
      
      {error && <div>Error: {error.message}</div>}
      {result && (
        <div>
          <h3>Result:</h3>
          <pre>{JSON.stringify(result, null, 2)}</pre>
        </div>
      )}
    </div>
  );
}
```

## Obtenir votre clé API

<Steps>
  <Step title="Connectez-vous à Sim">
    Accédez à [Sim](https://sim.ai) et connectez-vous à votre compte.
  </Step>
  <Step title="Ouvrez votre workflow">
    Accédez au workflow que vous souhaitez exécuter par programmation.
  </Step>
  <Step title="Déployez votre workflow">
    Cliquez sur « Déployer » pour déployer votre workflow s'il n'a pas encore été déployé.
  </Step>
  <Step title="Créez ou sélectionnez une clé API">
    Pendant le processus de déploiement, sélectionnez ou créez une clé API.
  </Step>
  <Step title="Copiez la clé API">
    Copiez la clé API à utiliser dans votre application TypeScript/JavaScript.
  </Step>
</Steps>

<Callout type="warning">
  Gardez votre clé API en sécurité et ne la soumettez jamais au contrôle de version. Utilisez des variables d'environnement ou une gestion de configuration sécurisée.
</Callout>

## Prérequis

- Node.js 16+
- TypeScript 5.0+ (pour les projets TypeScript)

## Support TypeScript

Le SDK est écrit en TypeScript et offre une sécurité de type complète :

```typescript
import { 
  SimStudioClient, 
  WorkflowExecutionResult, 
  WorkflowStatus,
  SimStudioError 
} from 'simstudio-ts-sdk';

// Type-safe client initialization
const client: SimStudioClient = new SimStudioClient({
  apiKey: process.env.SIMSTUDIO_API_KEY!
});

// Type-safe workflow execution
const result: WorkflowExecutionResult = await client.executeWorkflow('workflow-id', {
  input: {
    message: 'Hello, TypeScript!'
  }
});

// Type-safe status checking
const status: WorkflowStatus = await client.getWorkflowStatus('workflow-id');
```

## Licence

Apache-2.0