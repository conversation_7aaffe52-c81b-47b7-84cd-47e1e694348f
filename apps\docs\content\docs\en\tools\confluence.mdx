---
title: Confluence
description: Interact with Confluence
---

import { BlockInfoCard } from "@/components/ui/block-info-card"

<BlockInfoCard 
  type="confluence"
  color="#E0E0E0"
  icon={true}
  iconSvg={`<svg className="block-icon"
      
      
      
      viewBox='0 3 21 24'
      focusable='false'
      fill='none'
      aria-hidden='true'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        fill='#1868DB'
        d='M20.602 20.234c-6.584-3.183-8.507-3.66-11.281-3.66-3.255 0-6.03 1.355-8.507 5.16l-.407.622c-.333.513-.407.696-.407.915s.111.403.518.659l4.18 2.598c.221.146.406.22.591.22.222 0 .37-.11.592-.44l.666-1.024c1.035-1.574 1.96-2.086 3.144-2.086 1.035 0 2.256.293 3.772 1.025l4.365 2.049c.444.22.925.11 1.146-.403l2.072-4.537c.222-.512.074-.842-.444-1.098M1.406 12.22c6.583 3.184 8.507 3.66 11.28 3.66 3.256 0 6.03-1.354 8.508-5.16l.407-.622c.332-.512.406-.695.406-.915s-.11-.402-.518-.658L17.31 5.927c-.222-.147-.407-.22-.592-.22-.222 0-.37.11-.592.44l-.665 1.024c-1.036 1.573-1.96 2.086-3.144 2.086-1.036 0-2.257-.293-3.773-1.025L4.18 6.183c-.444-.22-.925-.11-1.147.402L.962 11.123c-.222.512-.074.841.444 1.098'
      />
    </svg>`}
/>

{/* MANUAL-CONTENT-START:intro */}
[Confluence](https://www.atlassian.com/software/confluence) is Atlassian's powerful team collaboration and knowledge management platform. It serves as a centralized workspace where teams can create, organize, and share information across departments and organizations.

With Confluence, you can:

- **Create structured documentation**: Build comprehensive wikis, project plans, and knowledge bases with rich formatting
- **Collaborate in real-time**: Work together on documents with teammates, with comments, mentions, and editing capabilities
- **Organize information hierarchically**: Structure content with spaces, pages, and nested hierarchies for intuitive navigation
- **Integrate with other tools**: Connect with Jira, Trello, and other Atlassian products for seamless workflow integration
- **Control access permissions**: Manage who can view, edit, or comment on specific content

In Sim, the Confluence integration enables your agents to access and leverage your organization's knowledge base. Agents can retrieve information from Confluence pages, search for specific content, and even update documentation when needed. This allows your workflows to incorporate the collective knowledge stored in your Confluence instance, making it possible to build agents that can reference internal documentation, follow established procedures, and maintain up-to-date information resources as part of their operations.
{/* MANUAL-CONTENT-END */}


## Usage Instructions

Connect to Confluence workspaces to retrieve and search documentation. Access page content, metadata, and integrate Confluence documentation into your workflows.



## Tools

### `confluence_retrieve`

Retrieve content from Confluence pages using the Confluence API.

#### Input

| Parameter | Type | Required | Description |
| --------- | ---- | -------- | ----------- |
| `domain` | string | Yes | Your Confluence domain \(e.g., yourcompany.atlassian.net\) |
| `pageId` | string | Yes | Confluence page ID to retrieve |
| `cloudId` | string | No | Confluence Cloud ID for the instance. If not provided, it will be fetched using the domain. |

#### Output

| Parameter | Type | Description |
| --------- | ---- | ----------- |
| `ts` | string | Timestamp of retrieval |
| `pageId` | string | Confluence page ID |
| `content` | string | Page content with HTML tags stripped |
| `title` | string | Page title |

### `confluence_update`

Update a Confluence page using the Confluence API.

#### Input

| Parameter | Type | Required | Description |
| --------- | ---- | -------- | ----------- |
| `domain` | string | Yes | Your Confluence domain \(e.g., yourcompany.atlassian.net\) |
| `pageId` | string | Yes | Confluence page ID to update |
| `title` | string | No | New title for the page |
| `content` | string | No | New content for the page in Confluence storage format |
| `version` | number | No | Version number of the page \(required for preventing conflicts\) |
| `cloudId` | string | No | Confluence Cloud ID for the instance. If not provided, it will be fetched using the domain. |

#### Output

| Parameter | Type | Description |
| --------- | ---- | ----------- |
| `ts` | string | Timestamp of update |
| `pageId` | string | Confluence page ID |
| `title` | string | Updated page title |
| `success` | boolean | Update operation success status |



## Notes

- Category: `tools`
- Type: `confluence`
