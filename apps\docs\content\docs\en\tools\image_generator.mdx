---
title: Image Generator
description: Generate images
---

import { BlockInfoCard } from "@/components/ui/block-info-card"

<BlockInfoCard 
  type="image_generator"
  color="#4D5FFF"
  icon={true}
  iconSvg={`<svg className="block-icon"
      
      
      
      viewBox='0 0 26 26'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
      stroke='currentColor'
      strokeWidth='2'
      strokeLinecap='round'
      strokeLinejoin='round'
    >
      <path d='M24.903 10.32C16.0897 9.10933 8.48966 15.6533 9.00033 24.3333M5.66699 7.66667C5.66699 8.37391 5.94794 9.05219 6.44804 9.55228C6.94814 10.0524 7.62641 10.3333 8.33366 10.3333C9.0409 10.3333 9.71918 10.0524 10.2193 9.55228C10.7194 9.05219 11.0003 8.37391 11.0003 7.66667C11.0003 6.95942 10.7194 6.28115 10.2193 5.78105C9.71918 5.28095 9.0409 5 8.33366 5C7.62641 5 6.94814 5.28095 6.44804 5.78105C5.94794 6.28115 5.66699 6.95942 5.66699 7.66667Z' />
      <path d='M1 14.4213C4.70667 13.908 8.03333 15.6986 9.832 18.5546' />
      <path d='M1 9.53333C1 6.54667 1 5.05333 1.58133 3.912C2.09265 2.90851 2.90851 2.09265 3.912 1.58133C5.05333 1 6.54667 1 9.53333 1H16.4667C19.4533 1 20.9467 1 22.088 1.58133C23.0915 2.09265 23.9073 2.90851 24.4187 3.912C25 5.05333 25 6.54667 25 9.53333V16.4667C25 19.4533 25 20.9467 24.4187 22.088C23.9073 23.0915 23.0915 23.9073 22.088 24.4187C20.9467 25 19.4533 25 16.4667 25H9.53333C6.54667 25 5.05333 25 3.912 24.4187C2.90851 23.9073 2.09265 23.0915 1.58133 22.088C1 20.9467 1 19.4533 1 16.4667V9.53333Z' />
    </svg>`}
/>

{/* MANUAL-CONTENT-START:intro */}
[DALL-E](https://openai.com/dall-e-3) is OpenAI's advanced AI system designed to generate realistic images and art from natural language descriptions. As a state-of-the-art image generation model, DALL-E can create detailed and creative visuals based on text prompts, allowing users to transform their ideas into visual content without requiring artistic skills.

With DALL-E, you can:

- **Generate realistic images**: Create photorealistic visuals from textual descriptions
- **Design conceptual art**: Transform abstract ideas into visual representations
- **Produce variations**: Generate multiple interpretations of the same prompt
- **Control artistic style**: Specify artistic styles, mediums, and visual aesthetics
- **Create detailed scenes**: Describe complex scenes with multiple elements and relationships
- **Visualize products**: Generate product mockups and design concepts
- **Illustrate ideas**: Turn written concepts into visual illustrations

In Sim, the DALL-E integration enables your agents to generate images programmatically as part of their workflows. This allows for powerful automation scenarios such as content creation, visual design, and creative ideation. Your agents can formulate detailed prompts, generate corresponding images, and incorporate these visuals into their outputs or downstream processes. This integration bridges the gap between natural language processing and visual content creation, enabling your agents to communicate not just through text but also through compelling imagery. By connecting Sim with DALL-E, you can create agents that produce visual content on demand, illustrate concepts, generate design assets, and enhance user experiences with rich visual elements - all without requiring human intervention in the creative process.
{/* MANUAL-CONTENT-END */}


## Usage Instructions

Create high-quality images using OpenAI's image generation models. Configure resolution, quality, style, and other parameters to get exactly the image you need.



## Tools

### `openai_image`

Generate images using OpenAI

#### Input

| Parameter | Type | Required | Description |
| --------- | ---- | -------- | ----------- |
| `model` | string | Yes | The model to use \(gpt-image-1 or dall-e-3\) |
| `prompt` | string | Yes | A text description of the desired image |
| `size` | string | Yes | The size of the generated images \(1024x1024, 1024x1792, or 1792x1024\) |
| `quality` | string | No | The quality of the image \(standard or hd\) |
| `style` | string | No | The style of the image \(vivid or natural\) |
| `background` | string | No | The background color, only for gpt-image-1 |
| `n` | number | No | The number of images to generate \(1-10\) |
| `apiKey` | string | Yes | Your OpenAI API key |

#### Output

| Parameter | Type | Description |
| --------- | ---- | ----------- |
| `success` | boolean | Operation success status |
| `output` | object | Generated image data |



## Notes

- Category: `tools`
- Type: `image_generator`
