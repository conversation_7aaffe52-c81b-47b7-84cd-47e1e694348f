---
title: Slack
description: Envoyez des messages à Slack ou déclenchez des workflows à partir
  d'événements Slack
---

import { BlockInfoCard } from "@/components/ui/block-info-card"

<BlockInfoCard 
  type="slack"
  color="#611f69"
  icon={true}
  iconSvg={`<svg className="block-icon" viewBox='0 0 256 256' xmlns='http://www.w3.org/2000/svg' >
      <g>
        <path
          d='M53.8412698,161.320635 C53.8412698,176.152381 41.8539683,188.139683 27.0222222,188.139683 C12.1904762,188.139683 0.203174603,176.152381 0.203174603,161.320635 C0.203174603,146.488889 12.1904762,134.501587 27.0222222,134.501587 L53.8412698,134.501587 L53.8412698,161.320635 Z M67.2507937,161.320635 C67.2507937,146.488889 79.2380952,134.501587 94.0698413,134.501587 C108.901587,134.501587 120.888889,146.488889 120.888889,161.320635 L120.888889,228.368254 C120.888889,243.2 108.901587,255.187302 94.0698413,255.187302 C79.2380952,255.187302 67.2507937,243.2 67.2507937,228.368254 L67.2507937,161.320635 Z'
          fill='#E01E5A'
        />
        <path
          d='M94.0698413,53.6380952 C79.2380952,53.6380952 67.2507937,41.6507937 67.2507937,26.8190476 C67.2507937,11.9873016 79.2380952,-7.10542736e-15 94.0698413,-7.10542736e-15 C108.901587,-7.10542736e-15 120.888889,11.9873016 120.888889,26.8190476 L120.888889,53.6380952 L94.0698413,53.6380952 Z M94.0698413,67.2507937 C108.901587,67.2507937 120.888889,79.2380952 120.888889,94.0698413 C120.888889,108.901587 108.901587,120.888889 94.0698413,120.888889 L26.8190476,120.888889 C11.9873016,120.888889 0,108.901587 0,94.0698413 C0,79.2380952 11.9873016,67.2507937 26.8190476,67.2507937 L94.0698413,67.2507937 Z'
          fill='#36C5F0'
        />
        <path
          d='M201.549206,94.0698413 C201.549206,79.2380952 213.536508,67.2507937 228.368254,67.2507937 C243.2,67.2507937 255.187302,79.2380952 255.187302,94.0698413 C255.187302,108.901587 243.2,120.888889 228.368254,120.888889 L201.549206,120.888889 L201.549206,94.0698413 Z M188.139683,94.0698413 C188.139683,108.901587 176.152381,120.888889 161.320635,120.888889 C146.488889,120.888889 134.501587,108.901587 134.501587,94.0698413 L134.501587,26.8190476 C134.501587,11.9873016 146.488889,-1.42108547e-14 161.320635,-1.42108547e-14 C176.152381,-1.42108547e-14 188.139683,11.9873016 188.139683,26.8190476 L188.139683,94.0698413 Z'
          fill='#2EB67D'
        />
        <path
          d='M161.320635,201.549206 C176.152381,201.549206 188.139683,213.536508 188.139683,228.368254 C188.139683,243.2 176.152381,255.187302 161.320635,255.187302 C146.488889,255.187302 134.501587,243.2 134.501587,228.368254 L134.501587,201.549206 L161.320635,201.549206 Z M161.320635,188.139683 C146.488889,188.139683 134.501587,176.152381 134.501587,161.320635 C134.501587,146.488889 146.488889,134.501587 161.320635,134.501587 L228.571429,134.501587 C243.403175,134.501587 255.390476,146.488889 255.390476,161.320635 C255.390476,176.152381 243.403175,188.139683 228.571429,188.139683 L161.320635,188.139683 Z'
          fill='#ECB22E'
        />
      </g>
    </svg>`}
/>

{/* MANUAL-CONTENT-START:intro */}
[Slack](https://www.slack.com/) est une plateforme de communication professionnelle qui offre aux équipes un espace unifié pour les messages, les outils et les fichiers.

<iframe
  width="100%"
  height="400"
  src="https://www.youtube.com/embed/J5jz3UaWmE8"
  title="Intégration de Slack avec Sim"
  frameBorder="0"
  allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
  allowFullScreen
></iframe>

Avec Slack, vous pouvez :

- **Automatiser les notifications des agents** : envoyer des mises à jour en temps réel depuis vos agents Sim vers n'importe quel canal Slack
- **Créer des points de terminaison webhook** : configurer des bots Slack comme webhooks pour déclencher des workflows Sim à partir d'activités Slack
- **Améliorer les workflows des agents** : intégrer la messagerie Slack dans vos agents pour livrer des résultats, des alertes et des mises à jour de statut
- **Créer et partager des canevas Slack** : générer programmatiquement des documents collaboratifs (canevas) dans les canaux Slack
- **Lire les messages des canaux** : récupérer et traiter les messages récents de n'importe quel canal Slack pour la surveillance ou le déclenchement de workflows

Dans Sim, l'intégration Slack permet à vos agents d'interagir de manière programmatique avec Slack de plusieurs façons dans le cadre de leurs flux de travail :

- **Envoyer des messages** : les agents peuvent envoyer des messages formatés à n'importe quel canal ou utilisateur Slack, prenant en charge la syntaxe mrkdwn de Slack pour un formatage enrichi.
- **Créer des canevas** : les agents peuvent créer et partager des canevas Slack (documents collaboratifs) directement dans les canaux, permettant un partage de contenu et une documentation plus riches.
- **Lire des messages** : les agents peuvent lire les messages récents des canaux, permettant la surveillance, la création de rapports ou le déclenchement d'actions supplémentaires basées sur l'activité du canal.

Cela permet des scénarios d'automatisation puissants tels que l'envoi de notifications, d'alertes, de mises à jour et de rapports directement vers le hub de communication de votre équipe, le partage de documents structurés ou la surveillance des conversations pour les déclencheurs de flux de travail. Vos agents peuvent fournir des informations opportunes, partager les résultats des processus qu'ils ont terminés, créer des documents collaboratifs ou alerter les membres de l'équipe lorsqu'une attention est nécessaire. Cette intégration comble le fossé entre vos flux de travail IA et la communication de votre équipe, garantissant que tout le monde reste informé sans intervention manuelle. En connectant Sim avec Slack, vous pouvez créer des agents qui tiennent votre équipe informée avec des informations pertinentes au bon moment, améliorent la collaboration en partageant automatiquement des insights, et réduisent le besoin de mises à jour manuelles de statut - tout en exploitant votre espace de travail Slack existant où votre équipe communique déjà.
{/* MANUAL-CONTENT-END */}

## Instructions d'utilisation

Intégration complète de Slack avec authentification OAuth. Envoyez des messages formatés en utilisant la syntaxe mrkdwn de Slack ou déclenchez des flux de travail à partir d'événements Slack comme les mentions et les messages.

## Outils

### `slack_message`

Envoyez des messages aux canaux ou utilisateurs Slack via l'API Slack. Prend en charge le formatage mrkdwn de Slack.

#### Entrée

| Paramètre | Type | Obligatoire | Description |
| --------- | ---- | ----------- | ----------- |
| `authMethod` | chaîne | Non | Méthode d'authentification : oauth ou bot_token |
| `botToken` | chaîne | Non | Jeton du bot pour le Bot personnalisé |
| `channel` | chaîne | Oui | Canal Slack cible (par ex., #general) |
| `text` | chaîne | Oui | Texte du message à envoyer (prend en charge le formatage mrkdwn de Slack) |

#### Sortie

| Paramètre | Type | Description |
| --------- | ---- | ----------- |
| `ts` | chaîne | Horodatage du message |
| `channel` | chaîne | ID du canal où le message a été envoyé |

### `slack_canvas`

Créer et partager des canevas Slack dans les canaux. Les canevas sont des documents collaboratifs au sein de Slack.

#### Entrée

| Paramètre | Type | Obligatoire | Description |
| --------- | ---- | ----------- | ----------- |
| `authMethod` | chaîne | Non | Méthode d'authentification : oauth ou bot_token |
| `botToken` | chaîne | Non | Jeton du bot pour le Bot personnalisé |
| `channel` | chaîne | Oui | Canal Slack cible (par ex., #general) |
| `title` | chaîne | Oui | Titre du canevas |
| `content` | chaîne | Oui | Contenu du canevas au format markdown |
| `document_content` | objet | Non | Contenu structuré du document canevas |

#### Sortie

| Paramètre | Type | Description |
| --------- | ---- | ----------- |
| `canvas_id` | chaîne | ID du canevas créé |
| `channel` | chaîne | Canal où le canevas a été créé |
| `title` | chaîne | Titre du canevas |

### `slack_message_reader`

Lisez les derniers messages des canaux Slack. Récupérez l'historique des conversations avec des options de filtrage.

#### Entrée

| Paramètre | Type | Obligatoire | Description |
| --------- | ---- | ---------- | ----------- |
| `authMethod` | chaîne | Non | Méthode d'authentification : oauth ou bot_token |
| `botToken` | chaîne | Non | Jeton du bot pour Bot personnalisé |
| `channel` | chaîne | Oui | Canal Slack pour lire les messages \(ex. : #general\) |
| `limit` | nombre | Non | Nombre de messages à récupérer \(par défaut : 10, max : 100\) |
| `oldest` | chaîne | Non | Début de la plage temporelle \(horodatage\) |
| `latest` | chaîne | Non | Fin de la plage temporelle \(horodatage\) |

#### Sortie

| Paramètre | Type | Description |
| --------- | ---- | ----------- |
| `messages` | tableau | Tableau d'objets de messages du canal |

## Remarques

- Catégorie : `tools`
- Type : `slack`
