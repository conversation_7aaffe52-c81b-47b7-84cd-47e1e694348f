---
title: Qdrant
description: Usa la base de datos vectorial Qdrant
---

import { BlockInfoCard } from "@/components/ui/block-info-card"

<BlockInfoCard 
  type="qdrant"
  color="#1A223F"
  icon={true}
  iconSvg={`<svg className="block-icon"  fill='none' viewBox='0 0 49 56' xmlns='http://www.w3.org/2000/svg'>
      <g clipPath='url(#b)'>
        <path
          d='m38.489 51.477-1.1167-30.787-2.0223-8.1167 13.498 1.429v37.242l-8.2456 4.7589-2.1138-4.5259z'
          clipRule='evenodd'
          fill='#24386C'
          fillRule='evenodd'
        />
        <path
          d='m48.847 14-8.2457 4.7622-17.016-3.7326-19.917 8.1094-3.3183-9.139 12.122-7 12.126-7 12.123 7 12.126 7z'
          clipRule='evenodd'
          fill='#7589BE'
          fillRule='evenodd'
        />
        <path
          d='m0.34961 13.999 8.2457 4.7622 4.7798 14.215 16.139 12.913-4.9158 10.109-12.126-7.0004-12.123-7v-28z'
          clipRule='evenodd'
          fill='#B2BFE8'
          fillRule='evenodd'
        />
        <path
          d='m30.066 38.421-5.4666 8.059v9.5207l7.757-4.4756 3.9968-5.9681'
          clipRule='evenodd'
          fill='#24386C'
          fillRule='evenodd'
        />
        <path
          d='m24.602 36.962-7.7603-13.436 1.6715-4.4531 6.3544-3.0809 7.488 7.5343-7.7536 13.436z'
          clipRule='evenodd'
          fill='#7589BE'
          fillRule='evenodd'
        />
        <path
          d='m16.843 23.525 7.7569 4.4756v8.9585l-7.1741 0.3087-4.3397-5.5412 3.7569-8.2016z'
          clipRule='evenodd'
          fill='#B2BFE8'
          fillRule='evenodd'
        />
        <path
          d='m24.6 28 7.757-4.4752 5.2792 8.7903-6.3886 5.2784-6.6476-0.6346v-8.9589z'
          clipRule='evenodd'
          fill='#24386C'
          fillRule='evenodd'
        />
        <path
          d='m32.355 51.524 8.2457 4.476v-37.238l-8.0032-4.6189-7.9995-4.6189-8.0031 4.6189-7.9995 4.6189v18.479l7.9995 4.6189 8.0031 4.6193 7.757-4.4797v9.5244zm0-19.045-7.757 4.4793-7.7569-4.4793v-8.9549l7.7569-4.4792 7.757 4.4792v8.9549z'
          clipRule='evenodd'
          fill='#DC244C'
          fillRule='evenodd'
        />
        <path d='m24.603 46.483v-9.5222l-7.7166-4.4411v9.5064l7.7166 4.4569z' fill='url(#a)' />
      </g>
      <defs>
        <linearGradient
          id='a'
          x1='23.18'
          x2='15.491'
          y1='38.781'
          y2='38.781'
          gradientUnits='userSpaceOnUse'
        >
          <stop stopColor='#FF3364' offset='0' />
          <stop stopColor='#C91540' stopOpacity='0' offset='1' />
        </linearGradient>
        <clipPath id='b'>
          <rect transform='translate(.34961)'   fill='#fff' />
        </clipPath>
      </defs>
    </svg>`}
/>

{/* MANUAL-CONTENT-START:intro */}
[Qdrant](https://qdrant.tech) es una base de datos vectorial de código abierto diseñada para el almacenamiento, gestión y recuperación eficiente de embeddings vectoriales de alta dimensionalidad. Qdrant permite búsquedas semánticas rápidas y escalables, lo que la hace ideal para aplicaciones de IA que requieren búsqueda por similitud, sistemas de recomendación y recuperación de información contextual.

Con Qdrant, puedes:

- **Almacenar embeddings vectoriales**: Gestionar y persistir vectores de alta dimensionalidad de manera eficiente y a escala
- **Realizar búsquedas de similitud semántica**: Encontrar los vectores más similares a un vector de consulta en tiempo real
- **Filtrar y organizar datos**: Utilizar filtrado avanzado para refinar los resultados de búsqueda basados en metadatos o payload
- **Obtener puntos específicos**: Recuperar vectores y sus payloads asociados por ID
- **Escalar sin problemas**: Manejar grandes colecciones y cargas de trabajo de alto rendimiento

En Sim, la integración con Qdrant permite a tus agentes interactuar con Qdrant de forma programática como parte de sus flujos de trabajo. Las operaciones compatibles incluyen:

- **Upsert**: Insertar o actualizar puntos (vectores y cargas útiles) en una colección de Qdrant
- **Search**: Realizar búsquedas de similitud para encontrar vectores más similares a un vector de consulta dado, con filtrado opcional y personalización de resultados
- **Fetch**: Recuperar puntos específicos de una colección por sus IDs, con opciones para incluir cargas útiles y vectores

Esta integración permite a tus agentes aprovechar potentes capacidades de búsqueda y gestión de vectores, habilitando escenarios avanzados de automatización como búsqueda semántica, recomendaciones y recuperación contextual. Al conectar Sim con Qdrant, puedes crear agentes que entiendan el contexto, recuperen información relevante de grandes conjuntos de datos y ofrezcan respuestas más inteligentes y personalizadas, todo sin gestionar infraestructuras complejas.
{/* MANUAL-CONTENT-END */}

## Instrucciones de uso

Almacena, busca y recupera incrustaciones vectoriales usando Qdrant. Realiza búsquedas de similitud semántica y gestiona tus colecciones de vectores.

## Herramientas

### `qdrant_upsert_points`

Insertar o actualizar puntos en una colección de Qdrant

#### Entrada

| Parámetro | Tipo | Obligatorio | Descripción |
| --------- | ---- | ----------- | ----------- |
| `url` | string | Sí | URL base de Qdrant |
| `apiKey` | string | No | Clave API de Qdrant \(opcional\) |
| `collection` | string | Sí | Nombre de la colección |
| `points` | array | Sí | Array de puntos para upsert |

#### Salida

| Parámetro | Tipo | Descripción |
| --------- | ---- | ----------- |
| `status` | string | Estado de la operación upsert |
| `data` | object | Datos de resultado de la operación upsert |

### `qdrant_search_vector`

Buscar vectores similares en una colección de Qdrant

#### Entrada

| Parámetro | Tipo | Obligatorio | Descripción |
| --------- | ---- | -------- | ----------- |
| `url` | string | Sí | URL base de Qdrant |
| `apiKey` | string | No | Clave API de Qdrant \(opcional\) |
| `collection` | string | Sí | Nombre de la colección |
| `vector` | array | Sí | Vector a buscar |
| `limit` | number | No | Número de resultados a devolver |
| `filter` | object | No | Filtro a aplicar a la búsqueda |
| `with_payload` | boolean | No | Incluir payload en la respuesta |
| `with_vector` | boolean | No | Incluir vector en la respuesta |

#### Salida

| Parámetro | Tipo | Descripción |
| --------- | ---- | ----------- |
| `data` | array | Resultados de búsqueda de vectores con ID, puntuación, payload y datos de vector opcionales |
| `status` | string | Estado de la operación de búsqueda |

### `qdrant_fetch_points`

Obtener puntos por ID desde una colección de Qdrant

#### Entrada

| Parámetro | Tipo | Obligatorio | Descripción |
| --------- | ---- | -------- | ----------- |
| `url` | string | Sí | URL base de Qdrant |
| `apiKey` | string | No | Clave API de Qdrant \(opcional\) |
| `collection` | string | Sí | Nombre de la colección |
| `ids` | array | Sí | Array de IDs de puntos a obtener |
| `with_payload` | boolean | No | Incluir payload en la respuesta |
| `with_vector` | boolean | No | Incluir vector en la respuesta |

#### Salida

| Parámetro | Tipo | Descripción |
| --------- | ---- | ----------- |
| `data` | array | Puntos recuperados con ID, carga útil y datos vectoriales opcionales |
| `status` | string | Estado de la operación de recuperación |

## Notas

- Categoría: `tools`
- Tipo: `qdrant`
