---
title: Microsoft Planner
description: Lire et créer des tâches dans Microsoft Planner
---

import { BlockInfoCard } from "@/components/ui/block-info-card"

<BlockInfoCard 
  type="microsoft_planner"
  color="#E0E0E0"
  icon={true}
  iconSvg={`<svg className="block-icon"  fill='currentColor' viewBox='-1 -1 27 27' xmlns='http://www.w3.org/2000/svg'>
      <defs>
        <linearGradient
          id='paint0_linear_3984_11038'
          x1='6.38724'
          y1='3.74167'
          x2='2.15779'
          y2='12.777'
          gradientUnits='userSpaceOnUse'
        >
          <stop stopColor='#8752E0' />
          <stop offset='1' stopColor='#541278' />
        </linearGradient>
        <linearGradient
          id='paint1_linear_3984_11038'
          x1='8.38032'
          y1='11.0696'
          x2='4.94062'
          y2='7.69244'
          gradientUnits='userSpaceOnUse'
        >
          <stop offset='0.12172' stopColor='#3D0D59' />
          <stop offset='1' stopColor='#7034B0' stopOpacity='0' />
        </linearGradient>
        <linearGradient
          id='paint2_linear_3984_11038'
          x1='18.3701'
          y1='-3.33385e-05'
          x2='9.85717'
          y2='20.4192'
          gradientUnits='userSpaceOnUse'
        >
          <stop stopColor='#DB45E0' />
          <stop offset='1' stopColor='#6C0F71' />
        </linearGradient>
        <linearGradient
          id='paint3_linear_3984_11038'
          x1='18.3701'
          y1='-3.33385e-05'
          x2='9.85717'
          y2='20.4192'
          gradientUnits='userSpaceOnUse'
        >
          <stop stopColor='#DB45E0' />
          <stop offset='0.677403' stopColor='#A829AE' />
          <stop offset='1' stopColor='#8F28B3' />
        </linearGradient>
        <linearGradient
          id='paint4_linear_3984_11038'
          x1='18.0002'
          y1='7.49958'
          x2='14.0004'
          y2='23.9988'
          gradientUnits='userSpaceOnUse'
        >
          <stop stopColor='#3DCBFF' />
          <stop offset='1' stopColor='#00479E' />
        </linearGradient>
        <linearGradient
          id='paint5_linear_3984_11038'
          x1='18.2164'
          y1='7.92626'
          x2='10.5237'
          y2='22.9363'
          gradientUnits='userSpaceOnUse'
        >
          <stop stopColor='#3DCBFF' />
          <stop offset='1' stopColor='#4A40D4' />
        </linearGradient>
      </defs>
      <path
        d='M8.25809 15.7412C7.22488 16.7744 5.54971 16.7744 4.5165 15.7412L0.774909 11.9996C-0.258303 10.9664 -0.258303 9.29129 0.774908 8.25809L4.5165 4.51655C5.54971 3.48335 7.22488 3.48335 8.25809 4.51655L11.9997 8.2581C13.0329 9.29129 13.0329 10.9664 11.9997 11.9996L8.25809 15.7412Z'
        fill='url(#paint0_linear_3984_11038)'
      />
      <path
        d='M8.25809 15.7412C7.22488 16.7744 5.54971 16.7744 4.5165 15.7412L0.774909 11.9996C-0.258303 10.9664 -0.258303 9.29129 0.774908 8.25809L4.5165 4.51655C5.54971 3.48335 7.22488 3.48335 8.25809 4.51655L11.9997 8.2581C13.0329 9.29129 13.0329 10.9664 11.9997 11.9996L8.25809 15.7412Z'
        fill='url(#paint1_linear_3984_11038)'
      />
      <path
        d='M0.774857 11.9999C1.80809 13.0331 3.48331 13.0331 4.51655 11.9999L15.7417 0.774926C16.7749 -0.258304 18.4501 -0.258309 19.4834 0.774914L23.225 4.51655C24.2583 5.54977 24.2583 7.22496 23.225 8.25819L11.9999 19.4832C10.9667 20.5164 9.29146 20.5164 8.25822 19.4832L0.774857 11.9999Z'
        fill='url(#paint2_linear_3984_11038)'
      />
      <path
        d='M0.774857 11.9999C1.80809 13.0331 3.48331 13.0331 4.51655 11.9999L15.7417 0.774926C16.7749 -0.258304 18.4501 -0.258309 19.4834 0.774914L23.225 4.51655C24.2583 5.54977 24.2583 7.22496 23.225 8.25819L11.9999 19.4832C10.9667 20.5164 9.29146 20.5164 8.25822 19.4832L0.774857 11.9999Z'
        fill='url(#paint3_linear_3984_11038)'
      />
      <path
        d='M4.51642 15.7413C5.54966 16.7746 7.22487 16.7746 8.25812 15.7413L15.7415 8.25803C16.7748 7.2248 18.45 7.2248 19.4832 8.25803L23.2249 11.9997C24.2582 13.0329 24.2582 14.7081 23.2249 15.7413L15.7415 23.2246C14.7083 24.2579 13.033 24.2579 11.9998 23.2246L4.51642 15.7413Z'
        fill='url(#paint4_linear_3984_11038)'
      />
      <path
        d='M4.51642 15.7413C5.54966 16.7746 7.22487 16.7746 8.25812 15.7413L15.7415 8.25803C16.7748 7.2248 18.45 7.2248 19.4832 8.25803L23.2249 11.9997C24.2582 13.0329 24.2582 14.7081 23.2249 15.7413L15.7415 23.2246C14.7083 24.2579 13.033 24.2579 11.9998 23.2246L4.51642 15.7413Z'
        fill='url(#paint5_linear_3984_11038)'
      />
    </svg>`}
/>

{/* MANUAL-CONTENT-START:intro */}
[Microsoft Planner](https://www.microsoft.com/en-us/microsoft-365/planner) est un outil de gestion des tâches qui aide les équipes à organiser visuellement leur travail à l'aide de tableaux, de tâches et de compartiments. Intégré à Microsoft 365, il offre un moyen simple et intuitif de gérer les projets d'équipe, d'attribuer des responsabilités et de suivre la progression.

Avec Microsoft Planner, vous pouvez :

- **Créer et gérer des tâches** : ajouter de nouvelles tâches avec des dates d'échéance, des priorités et des utilisateurs assignés
- **Organiser avec des compartiments** : regrouper les tâches par phase, statut ou catégorie pour refléter le flux de travail de votre équipe
- **Visualiser l'état du projet** : utiliser des tableaux, des graphiques et des filtres pour surveiller la charge de travail et suivre la progression
- **Rester intégré à Microsoft 365** : connecter facilement les tâches avec Teams, Outlook et d'autres outils Microsoft

Dans Sim, l'intégration de Microsoft Planner permet à vos agents de créer, lire et gérer des tâches de manière programmatique dans le cadre de leurs flux de travail. Les agents peuvent générer de nouvelles tâches basées sur les demandes entrantes, récupérer les détails des tâches pour prendre des décisions et suivre l'état d'avancement des projets — le tout sans intervention humaine. Que vous construisiez des flux de travail pour l'intégration de clients, le suivi de projets internes ou la génération de tâches de suivi, l'intégration de Microsoft Planner avec Sim offre à vos agents un moyen structuré de coordonner le travail, d'automatiser la création de tâches et de maintenir les équipes alignées.
{/* MANUAL-CONTENT-END */}

## Instructions d'utilisation

Intégrez les fonctionnalités de Microsoft Planner pour gérer les tâches. Lisez toutes les tâches utilisateur, les tâches de plans spécifiques, des tâches individuelles, ou créez de nouvelles tâches avec diverses propriétés comme le titre, la description, la date d'échéance et les assignés en utilisant l'authentification OAuth.

## Outils

### `microsoft_planner_read_task`

Lire les tâches depuis Microsoft Planner - obtenir toutes les tâches de l'utilisateur ou toutes les tâches d'un plan spécifique

#### Entrée

| Paramètre | Type | Obligatoire | Description |
| --------- | ---- | -------- | ----------- |
| `planId` | chaîne | Non | L'identifiant du plan à partir duquel obtenir les tâches \(si non fourni, récupère toutes les tâches de l'utilisateur\) |
| `taskId` | chaîne | Non | L'identifiant de la tâche à obtenir |

#### Sortie

| Paramètre | Type | Description |
| --------- | ---- | ----------- |
| `success` | booléen | Indique si les tâches ont été récupérées avec succès |
| `tasks` | tableau | Tableau d'objets de tâche avec propriétés filtrées |
| `metadata` | objet | Métadonnées incluant planId, userId et planUrl |

### `microsoft_planner_create_task`

Créer une nouvelle tâche dans Microsoft Planner

#### Entrée

| Paramètre | Type | Obligatoire | Description |
| --------- | ---- | -------- | ----------- |
| `planId` | chaîne | Oui | L'identifiant du plan où la tâche sera créée |
| `title` | chaîne | Oui | Le titre de la tâche |
| `description` | chaîne | Non | La description de la tâche |
| `dueDateTime` | chaîne | Non | La date et l'heure d'échéance pour la tâche \(format ISO 8601\) |
| `assigneeUserId` | chaîne | Non | L'identifiant de l'utilisateur à qui attribuer la tâche |
| `bucketId` | chaîne | Non | L'identifiant du compartiment où placer la tâche |

#### Sortie

| Paramètre | Type | Description |
| --------- | ---- | ----------- |
| `success` | booléen | Indique si la tâche a été créée avec succès |
| `task` | objet | L'objet de tâche créé avec toutes ses propriétés |
| `metadata` | objet | Métadonnées incluant planId, taskId et taskUrl |

## Notes

- Catégorie : `tools`
- Type : `microsoft_planner`
