---
title: Wikipedia
description: <PERSON><PERSON><PERSON> et récupérer du contenu depuis Wikipedia
---

import { BlockInfoCard } from "@/components/ui/block-info-card"

<BlockInfoCard 
  type="wikipedia"
  color="#000000"
  icon={true}
  iconSvg={`<svg className="block-icon"
      
      fill='currentColor'
      version='1.1'
      id='Capa_1'
      xmlns='http://www.w3.org/2000/svg'
      xmlnsXlink='http://www.w3.org/1999/xlink'
      
      
      viewBox='0 0 98.05 98.05'
      xmlSpace='preserve'
    >
      <g>
        <path
          d='M98.023,17.465l-19.584-0.056c-0.004,0.711-0.006,1.563-0.017,2.121c1.664,0.039,5.922,0.822,7.257,4.327L66.92,67.155
		c-0.919-2.149-9.643-21.528-10.639-24.02l9.072-18.818c1.873-2.863,5.455-4.709,8.918-4.843l-0.01-1.968L55.42,17.489
		c-0.045,0.499,0.001,1.548-0.068,2.069c5.315,0.144,7.215,1.334,5.941,4.508c-2.102,4.776-6.51,13.824-7.372,15.475
		c-2.696-5.635-4.41-9.972-7.345-16.064c-1.266-2.823,1.529-3.922,4.485-4.004v-1.981l-21.82-0.067
		c0.016,0.93-0.021,1.451-0.021,2.131c3.041,0.046,6.988,0.371,8.562,3.019c2.087,4.063,9.044,20.194,11.149,24.514
		c-2.685,5.153-9.207,17.341-11.544,21.913c-3.348-7.43-15.732-36.689-19.232-44.241c-1.304-3.218,3.732-5.077,6.646-5.213
		l0.019-2.148L0,17.398c0.005,0.646,0.027,1.71,0.029,2.187c4.025-0.037,9.908,6.573,11.588,10.683
		c7.244,16.811,14.719,33.524,21.928,50.349c0.002,0.029,2.256,0.059,2.281,0.008c4.717-9.653,10.229-19.797,15.206-29.56
		L63.588,80.64c0.005,0.004,2.082,0.016,2.093,0.007c7.962-18.196,19.892-46.118,23.794-54.933c1.588-3.767,4.245-6.064,8.543-6.194
		l0.032-1.956L98.023,17.465z'
        />
      </g>
    </svg>`}
/>

{/* MANUAL-CONTENT-START:intro */}
[Wikipedia](https://www.wikipedia.org/) est la plus grande encyclopédie gratuite en ligne au monde, offrant des millions d'articles sur une vaste gamme de sujets, rédigés et maintenus de manière collaborative par des bénévoles.

Avec Wikipedia, vous pouvez :

- **Rechercher des articles** : trouvez des pages Wikipedia pertinentes en recherchant des mots-clés ou des sujets
- **Obtenir des résumés d'articles** : récupérez des résumés concis des pages Wikipedia pour une référence rapide
- **Accéder au contenu complet** : obtenez le contenu intégral des articles Wikipedia pour des informations approfondies
- **Découvrir des articles aléatoires** : explorez de nouveaux sujets en récupérant des pages Wikipedia au hasard

Dans Sim, l'intégration de Wikipedia permet à vos agents d'accéder et d'interagir programmatiquement avec le contenu de Wikipedia dans le cadre de leurs flux de travail. Les agents peuvent rechercher des articles, récupérer des résumés, obtenir le contenu complet des pages et découvrir des articles aléatoires, ce qui permet à vos automatisations de disposer d'informations fiables et à jour provenant de la plus grande encyclopédie du monde. Cette intégration est idéale pour des scénarios tels que la recherche, l'enrichissement de contenu, la vérification des faits et la découverte de connaissances, permettant à vos agents d'incorporer facilement les données de Wikipedia dans leurs processus de prise de décision et d'exécution des tâches.
{/* MANUAL-CONTENT-END */}

## Instructions d'utilisation

Accédez aux articles Wikipedia, recherchez des pages, obtenez des résumés, récupérez le contenu complet et découvrez des articles aléatoires de la plus grande encyclopédie du monde.

## Outils

### `wikipedia_summary`

Obtenez un résumé et des métadonnées pour une page Wikipedia spécifique.

#### Entrée

| Paramètre | Type | Obligatoire | Description |
| --------- | ---- | ----------- | ----------- |
| `pageTitle` | string | Oui | Titre de la page Wikipedia dont vous souhaitez obtenir le résumé |

#### Sortie

| Paramètre | Type | Description |
| --------- | ---- | ----------- |
| `summary` | objet | Résumé et métadonnées de la page Wikipédia |

### `wikipedia_search`

Rechercher des pages Wikipédia par titre ou contenu.

#### Entrée

| Paramètre | Type | Obligatoire | Description |
| --------- | ---- | ----------- | ----------- |
| `query` | chaîne | Oui | Requête de recherche pour trouver des pages Wikipédia |
| `searchLimit` | nombre | Non | Nombre maximum de résultats à retourner \(par défaut : 10, max : 50\) |

#### Sortie

| Paramètre | Type | Description |
| --------- | ---- | ----------- |
| `searchResults` | tableau | Tableau des pages Wikipédia correspondantes |

### `wikipedia_content`

Obtenir le contenu HTML complet d'une page Wikipédia.

#### Entrée

| Paramètre | Type | Obligatoire | Description |
| --------- | ---- | ----------- | ----------- |
| `pageTitle` | chaîne | Oui | Titre de la page Wikipédia dont on veut obtenir le contenu |

#### Sortie

| Paramètre | Type | Description |
| --------- | ---- | ----------- |
| `content` | objet | Contenu HTML complet et métadonnées de la page Wikipédia |

### `wikipedia_random`

Obtenir une page Wikipédia aléatoire.

#### Entrée

| Paramètre | Type | Obligatoire | Description |
| --------- | ---- | ----------- | ----------- |

#### Sortie

| Paramètre | Type | Description |
| --------- | ---- | ----------- |
| `randomPage` | objet | Données d'une page Wikipédia aléatoire |

## Notes

- Catégorie : `tools`
- Type : `wikipedia`
