---
title: X
description: Interact with X
---

import { BlockInfoCard } from "@/components/ui/block-info-card"

<BlockInfoCard 
  type="x"
  color="#000000"
  icon={true}
  iconSvg={`<svg className="block-icon" xmlns='http://www.w3.org/2000/svg' viewBox='0 0 50 50'   >
      <path
        d='M 5.9199219 6 L 20.582031 27.375 L 6.2304688 44 L 9.4101562 44 L 21.986328 29.421875 L 31.986328 44 L 44 44 L 28.681641 21.669922 L 42.199219 6 L 39.029297 6 L 27.275391 19.617188 L 17.933594 6 L 5.9199219 6 z M 9.7167969 8 L 16.880859 8 L 40.203125 42 L 33.039062 42 L 9.7167969 8 z'
        fill='currentColor'
      />
    </svg>`}
/>

{/* MANUAL-CONTENT-START:intro */}
[X](https://x.com/) (formerly Twitter) is a popular social media platform that enables real-time communication, content sharing, and engagement with audiences worldwide.

The X integration in Sim leverages OAuth authentication to securely connect with the X API, allowing your agents to interact with the platform programmatically. This OAuth implementation ensures secure access to X's features while maintaining user privacy and security.

With the X integration, your agents can:

- **Post content**: Create new tweets, reply to existing conversations, or share media directly from your workflows
- **Monitor conversations**: Track mentions, keywords, or specific accounts to stay informed about relevant discussions
- **Engage with audiences**: Automatically respond to mentions, direct messages, or specific triggers
- **Analyze trends**: Gather insights from trending topics, hashtags, or user engagement patterns
- **Research information**: Search for specific content, user profiles, or conversations to inform agent decisions

In Sim, the X integration enables sophisticated social media automation scenarios. Your agents can monitor brand mentions and respond appropriately, schedule and publish content based on specific triggers, conduct social listening for market research, or create interactive experiences that span both conversational AI and social media engagement. By connecting Sim with X through OAuth, you can build intelligent agents that maintain a consistent and responsive social media presence while adhering to platform policies and best practices for API usage.
{/* MANUAL-CONTENT-END */}


## Usage Instructions

Connect with X to post tweets, read content, search for information, and access user profiles. Integrate social media capabilities into your workflow with comprehensive X platform access.



## Tools

### `x_write`

Post new tweets, reply to tweets, or create polls on X (Twitter)

#### Input

| Parameter | Type | Required | Description |
| --------- | ---- | -------- | ----------- |
| `text` | string | Yes | The text content of your tweet |
| `replyTo` | string | No | ID of the tweet to reply to |
| `mediaIds` | array | No | Array of media IDs to attach to the tweet |
| `poll` | object | No | Poll configuration for the tweet |

#### Output

| Parameter | Type | Description |
| --------- | ---- | ----------- |
| `tweet` | object | The newly created tweet data |

### `x_read`

Read tweet details, including replies and conversation context

#### Input

| Parameter | Type | Required | Description |
| --------- | ---- | -------- | ----------- |
| `tweetId` | string | Yes | ID of the tweet to read |
| `includeReplies` | boolean | No | Whether to include replies to the tweet |

#### Output

| Parameter | Type | Description |
| --------- | ---- | ----------- |
| `tweet` | object | The main tweet data |

### `x_search`

Search for tweets using keywords, hashtags, or advanced queries

#### Input

| Parameter | Type | Required | Description |
| --------- | ---- | -------- | ----------- |
| `query` | string | Yes | Search query \(supports X search operators\) |
| `maxResults` | number | No | Maximum number of results to return \(default: 10, max: 100\) |
| `startTime` | string | No | Start time for search \(ISO 8601 format\) |
| `endTime` | string | No | End time for search \(ISO 8601 format\) |
| `sortOrder` | string | No | Sort order for results \(recency or relevancy\) |

#### Output

| Parameter | Type | Description |
| --------- | ---- | ----------- |
| `tweets` | array | Array of tweets matching the search query |

### `x_user`

Get user profile information

#### Input

| Parameter | Type | Required | Description |
| --------- | ---- | -------- | ----------- |
| `username` | string | Yes | Username to look up \(without @ symbol\) |

#### Output

| Parameter | Type | Description |
| --------- | ---- | ----------- |
| `user` | object | X user profile information |



## Notes

- Category: `tools`
- Type: `x`
