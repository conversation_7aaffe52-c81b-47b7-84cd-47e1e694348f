---
title: Esquema YAML del bloque de flujo de trabajo
description: Referencia de configuración YAML para bloques de flujo de trabajo
---

## Definición del esquema

```yaml
type: object
required:
  - type
  - name
  - inputs
properties:
  type:
    type: string
    enum: [workflow]
    description: Block type identifier
  name:
    type: string
    description: Display name for this workflow block
  inputs:
    type: object
    required:
      - workflowId
    properties:
      workflowId:
        type: string
        description: ID of the workflow to execute
      inputMapping:
        type: object
        description: Map current workflow data to sub-workflow inputs
        additionalProperties:
          type: string
          description: Input value or reference to parent workflow data
      environmentVariables:
        type: object
        description: Environment variables to pass to sub-workflow
        additionalProperties:
          type: string
          description: Environment variable value
      timeout:
        type: number
        description: Maximum execution time in milliseconds
        default: 300000
        minimum: 1000
        maximum: 1800000
  connections:
    type: object
    properties:
      success:
        type: string
        description: Target block ID for successful workflow completion
      error:
        type: string
        description: Target block ID for error handling
```

## Configuración de conexión

Las conexiones definen hacia dónde va el flujo de trabajo según los resultados del subflujo de trabajo:

```yaml
connections:
  success: <string>                     # Target block ID for successful completion
  error: <string>                       # Target block ID for error handling (optional)
```

## Ejemplos

### Ejecución simple de flujo de trabajo

```yaml
data-processor:
  type: workflow
  name: "Data Processing Workflow"
  inputs:
    workflowId: "data-processing-v2"
    inputMapping:
      rawData: <start.input>
      userId: <user-validator.userId>
    environmentVariables:
      PROCESSING_MODE: "production"
      LOG_LEVEL: "info"
  connections:
    success: process-results
    error: workflow-error-handler
```

### Pipeline de generación de contenido

```yaml
content-generator:
  type: workflow
  name: "Content Generation Pipeline"
  inputs:
    workflowId: "content-generation-v3"
    inputMapping:
      topic: <start.topic>
      style: <style-analyzer.recommendedStyle>
      targetAudience: <audience-detector.audience>
      brandGuidelines: <brand-config.guidelines>
    environmentVariables:
      CONTENT_API_KEY: "{{CONTENT_API_KEY}}"
      QUALITY_THRESHOLD: "high"
    timeout: 120000
  connections:
    success: review-content
    error: content-generation-failed
```

### Flujo de trabajo de análisis de múltiples pasos

```yaml
analysis-workflow:
  type: workflow
  name: "Analysis Workflow"
  inputs:
    workflowId: "comprehensive-analysis"
    inputMapping:
      document: <document-processor.content>
      analysisType: "comprehensive"
      includeMetrics: true
      outputFormat: "structured"
    environmentVariables:
      ANALYSIS_MODEL: "gpt-4o"
      OPENAI_API_KEY: "{{OPENAI_API_KEY}}"
      CLAUDE_API_KEY: "{{CLAUDE_API_KEY}}"
  connections:
    success: compile-analysis-report
    error: analysis-workflow-error
```

### Ejecución condicional de flujo de trabajo

```yaml
customer-workflow-router:
  type: condition
  name: "Customer Workflow Router"
  inputs:
    conditions:
      if: <customer-type.type> === "enterprise"
      else-if: <customer-type.type> === "premium"
      else: true
  connections:
    conditions:
      if: enterprise-workflow
      else-if: premium-workflow  
      else: standard-workflow

enterprise-workflow:
  type: workflow
  name: "Enterprise Customer Workflow"
  inputs:
    workflowId: "enterprise-customer-processing"
    inputMapping:
      customerData: <customer-data.profile>
      accountManager: <account-assignment.manager>
      tier: "enterprise"
    environmentVariables:
      PRIORITY_LEVEL: "high"
      SLA_REQUIREMENTS: "strict"
  connections:
    success: enterprise-complete

premium-workflow:
  type: workflow
  name: "Premium Customer Workflow"
  inputs:
    workflowId: "premium-customer-processing"
    inputMapping:
      customerData: <customer-data.profile>
      supportLevel: "premium"
    environmentVariables:
      PRIORITY_LEVEL: "medium"
  connections:
    success: premium-complete

standard-workflow:
  type: workflow
  name: "Standard Customer Workflow"
  inputs:
    workflowId: "standard-customer-processing"
    inputMapping:
      customerData: <customer-data.profile>
    environmentVariables:
      PRIORITY_LEVEL: "standard"
  connections:
    success: standard-complete
```

### Ejecución paralela de flujo de trabajo

```yaml
parallel-workflows:
  type: parallel
  name: "Parallel Workflow Processing"
  inputs:
    parallelType: collection
    collection: |
      [
        {"workflowId": "sentiment-analysis", "focus": "sentiment"},
        {"workflowId": "topic-extraction", "focus": "topics"},
        {"workflowId": "entity-recognition", "focus": "entities"}
      ]
  connections:
    success: merge-workflow-results

execute-analysis-workflow:
  type: workflow
  name: "Execute Analysis Workflow"
  parentId: parallel-workflows
  inputs:
    workflowId: <parallel.currentItem.workflowId>
    inputMapping:
      content: <start.content>
      analysisType: <parallel.currentItem.focus>
    environmentVariables:
      ANALYSIS_API_KEY: "{{ANALYSIS_API_KEY}}"
  connections:
    success: workflow-complete
```

### Flujo de trabajo de manejo de errores

```yaml
main-workflow:
  type: workflow
  name: "Main Processing Workflow"
  inputs:
    workflowId: "main-processing-v1"
    inputMapping:
      data: <start.input>
    timeout: 180000
  connections:
    success: main-complete
    error: error-recovery-workflow

error-recovery-workflow:
  type: workflow
  name: "Error Recovery Workflow"
  inputs:
    workflowId: "error-recovery-v1"
    inputMapping:
      originalInput: <start.input>
      errorDetails: <main-workflow.error>
      failureTimestamp: "{{new Date().toISOString()}}"
    environmentVariables:
      RECOVERY_MODE: "automatic"
      FALLBACK_ENABLED: "true"
  connections:
    success: recovery-complete
    error: manual-intervention-required
```

## Mapeo de entrada

Mapea datos desde el flujo de trabajo principal al subflujo de trabajo:

```yaml
inputMapping:
  # Static values
  mode: "production"
  version: "1.0"
  
  # References to parent workflow data
  userData: <user-processor.profile>
  settings: <config-loader.settings>
  
  # Complex object mapping
  requestData:
    id: <start.requestId>
    timestamp: "{{new Date().toISOString()}}"
    source: "parent-workflow"
```

## Referencias de salida

Después de que un bloque de flujo de trabajo se completa, puedes hacer referencia a sus salidas:

```yaml
# In subsequent blocks
next-block:
  inputs:
    workflowResult: <workflow-name.output>    # Sub-workflow output
    executionTime: <workflow-name.duration>  # Execution duration
    status: <workflow-name.status>           # Execution status
```

## Mejores prácticas

- Utiliza identificadores descriptivos para los flujos de trabajo para mayor claridad
- Mapea solo los datos necesarios a los subflujos de trabajo
- Establece tiempos de espera apropiados según la complejidad del flujo de trabajo
- Incluye manejo de errores para una ejecución robusta
- Pasa las variables de entorno de forma segura
- Prueba los subflujos de trabajo de forma independiente primero
- Monitorea el rendimiento de los flujos de trabajo anidados
- Utiliza identificadores de flujos de trabajo versionados para mayor estabilidad