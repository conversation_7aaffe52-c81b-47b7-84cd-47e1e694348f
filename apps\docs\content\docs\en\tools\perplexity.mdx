---
title: Perplexity
description: Use Perplexity AI chat models
---

import { BlockInfoCard } from "@/components/ui/block-info-card"

<BlockInfoCard 
  type="perplexity"
  color="#20808D"
  icon={true}
  iconSvg={`<svg className="block-icon"   viewBox='0 0 24 24' xmlns='http://www.w3.org/2000/svg' >
      <path
        d='M19.785 0v7.272H22.5V17.62h-2.935V24l-7.037-6.194v6.145h-1.091v-6.152L4.392 24v-6.465H1.5V7.188h2.884V0l7.053 6.494V.19h1.09v6.49L19.786 0zm-7.257 9.044v7.319l5.946 5.234V14.44l-5.946-5.397zm-1.099-.08l-5.946 5.398v7.235l5.946-5.234V8.965zm8.136 7.58h1.844V8.349H13.46l6.105 5.54v2.655zm-8.982-8.28H2.59v8.195h1.8v-2.576l6.192-5.62zM5.475 2.476v4.71h5.115l-5.115-4.71zm13.219 0l-5.115 4.71h5.115v-4.71z'
        fill='currentColor'
        fillRule='nonzero'
      />
    </svg>`}
/>

{/* MANUAL-CONTENT-START:intro */}
[Perplexity AI](https://www.perplexity.ai) is an AI-powered search engine and answer engine that combines the capabilities of large language models with real-time web search to provide accurate, up-to-date information and comprehensive answers to complex questions.

With Perplexity AI, you can:

- **Get accurate answers**: Receive comprehensive responses to questions with citations from reliable sources
- **Access real-time information**: Obtain up-to-date information through Perplexity's web search capabilities
- **Explore topics in depth**: Dive deeper into subjects with follow-up questions and related information
- **Verify information**: Check the credibility of answers through provided sources and references
- **Generate content**: Create summaries, analyses, and creative content based on current information
- **Research efficiently**: Streamline research processes with comprehensive answers to complex queries
- **Interact conversationally**: Engage in natural dialogue to refine questions and explore topics

In Sim, the Perplexity integration enables your agents to leverage these powerful AI capabilities programmatically as part of their workflows. This allows for sophisticated automation scenarios that combine natural language understanding, real-time information retrieval, and content generation. Your agents can formulate queries, receive comprehensive answers with citations, and incorporate this information into their decision-making processes or outputs. This integration bridges the gap between your workflow automation and access to current, reliable information, enabling your agents to make more informed decisions and provide more accurate responses. By connecting Sim with Perplexity, you can create agents that stay current with the latest information, provide well-researched answers, and deliver more valuable insights to users - all without requiring manual research or information gathering.
{/* MANUAL-CONTENT-END */}


## Usage Instructions

Generate completions using Perplexity AI models with real-time knowledge and search capabilities. Create responses, answer questions, and generate content with customizable parameters.



## Tools

### `perplexity_chat`

Generate completions using Perplexity AI chat models

#### Input

| Parameter | Type | Required | Description |
| --------- | ---- | -------- | ----------- |
| `systemPrompt` | string | No | System prompt to guide the model behavior |
| `content` | string | Yes | The user message content to send to the model |
| `model` | string | Yes | Model to use for chat completions \(e.g., sonar, mistral\) |
| `max_tokens` | number | No | Maximum number of tokens to generate |
| `temperature` | number | No | Sampling temperature between 0 and 1 |
| `apiKey` | string | Yes | Perplexity API key |

#### Output

| Parameter | Type | Description |
| --------- | ---- | ----------- |
| `success` | boolean | Operation success status |
| `output` | object | Chat completion results |



## Notes

- Category: `tools`
- Type: `perplexity`
