---
title: Embeddings
description: Generar embeddings de Open AI
---

import { BlockInfoCard } from "@/components/ui/block-info-card"

<BlockInfoCard 
  type="openai"
  color="#10a37f"
  icon={true}
  iconSvg={`<svg className="block-icon"
      
      
      
      viewBox='0 0 24 24'
      role='img'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        d='M22.2819 9.8211a5.9847 5.9847 0 0 0-.5157-4.9108 6.0462 6.0462 0 0 0-6.5098-2.9A6.0651 6.0651 0 0 0 4.9807 4.1818a5.9847 5.9847 0 0 0-3.9977 2.9 6.0462 6.0462 0 0 0 .7427 7.0966 5.98 5.98 0 0 0 .511 4.9107 6.051 6.051 0 0 0 6.5146 2.9001A5.9847 5.9847 0 0 0 13.2599 24a6.0557 6.0557 0 0 0 5.7718-4.2058 5.9894 5.9894 0 0 0 3.9977-2.9001 6.0557 6.0557 0 0 0-.7475-7.0729zm-9.022 12.6081a4.4755 4.4755 0 0 1-2.8764-1.0408l.1419-.0804 4.7783-2.7582a.7948.7948 0 0 0 .3927-.6813v-6.7369l2.02 1.1686a.071.071 0 0 1 .038.052v5.5826a4.504 4.504 0 0 1-4.4945 4.4944zm-9.6607-4.1254a4.4708 4.4708 0 0 1-.5346-3.0137l.142.0852 4.783 2.7582a.7712.7712 0 0 0 .7806 0l5.8428-3.3685v2.3324a.0804.0804 0 0 1-.0332.0615L9.74 19.9502a4.4992 4.4992 0 0 1-6.1408-1.6464zM2.3408 7.8956a4.485 4.485 0 0 1 2.3655-1.9728V11.6a.7664.7664 0 0 0 .3879.6765l5.8144 3.3543-2.0201 1.1685a.0757.0757 0 0 1-.071 0l-4.8303-2.7865A4.504 4.504 0 0 1 2.3408 7.872zm16.5963 3.8558L13.1038 8.364 15.1192 7.2a.0757.0757 0 0 1 .071 0l4.8303 2.7913a4.4944 4.4944 0 0 1-.6765 8.1042v-5.6772a.79.79 0 0 0-.407-.667zm2.0107-3.0231l-.142-.0852-4.7735-2.7818a.7759.7759 0 0 0-.7854 0L9.409 9.2297V6.8974a.0662.0662 0 0 1 .0284-.0615l4.8303-2.7866a4.4992 4.4992 0 0 1 6.6802 4.66zM8.3065 12.863l-2.02-1.1638a.0804.0804 0 0 1-.038-.0567V6.0742a4.4992 4.4992 0 0 1 7.3757-3.4537l-.142.0805L8.704 5.459a.7948.7948 0 0 0-.3927.6813zm1.0976-2.3654l2.602-1.4998 2.6069 1.4998v2.9994l-2.5974 1.4997-2.6067-1.4997Z'
        fill='currentColor'
      />
    </svg>`}
/>

{/* MANUAL-CONTENT-START:intro */}
[OpenAI](https://www.openai.com) es una empresa líder en investigación y despliegue de IA que ofrece una suite de potentes modelos y APIs de IA. OpenAI proporciona tecnologías de vanguardia que incluyen modelos de lenguaje grandes (como GPT-4), generación de imágenes (DALL-E) y embeddings que permiten a los desarrolladores crear aplicaciones sofisticadas impulsadas por IA.

Con OpenAI, puedes:

- **Generar texto**: Crea texto similar al humano para diversas aplicaciones usando modelos GPT
- **Crear imágenes**: Transforma descripciones textuales en contenido visual con DALL-E
- **Producir embeddings**: Convierte texto en vectores numéricos para búsqueda semántica y análisis
- **Construir asistentes de IA**: Desarrolla agentes conversacionales con conocimiento especializado
- **Procesar y analizar datos**: Extrae insights y patrones de texto no estructurado
- **Traducir idiomas**: Convierte contenido entre diferentes idiomas con alta precisión
- **Resumir contenido**: Condensa texto extenso preservando la información clave

En Sim, la integración de OpenAI permite a tus agentes aprovechar estas potentes capacidades de IA de forma programática como parte de sus flujos de trabajo. Esto permite escenarios de automatización sofisticados que combinan comprensión del lenguaje natural, generación de contenido y análisis semántico. Tus agentes pueden generar embeddings vectoriales a partir de texto, que son representaciones numéricas que capturan el significado semántico, permitiendo sistemas avanzados de búsqueda, clasificación y recomendación. Además, a través de la integración con DALL-E, los agentes pueden crear imágenes a partir de descripciones textuales, abriendo posibilidades para la generación de contenido visual. Esta integración cierra la brecha entre tu automatización de flujos de trabajo y las capacidades de IA de vanguardia, permitiendo a tus agentes entender el contexto, generar contenido relevante y tomar decisiones inteligentes basadas en la comprensión semántica. Al conectar Sim con OpenAI, puedes crear agentes que procesen información de manera más inteligente, generen contenido creativo y ofrezcan experiencias más personalizadas a los usuarios.
{/* MANUAL-CONTENT-END */}

## Instrucciones de uso

Convierte texto en representaciones vectoriales numéricas utilizando los modelos de embedding de OpenAI. Transforma datos de texto en embeddings para búsqueda semántica, agrupación y otras operaciones basadas en vectores.

## Herramientas

### `openai_embeddings`

Generar embeddings a partir de texto usando OpenAI

#### Entrada

| Parámetro | Tipo | Obligatorio | Descripción |
| --------- | ---- | -------- | ----------- |
| `input` | string | Sí | Texto para generar embeddings |
| `model` | string | No | Modelo a utilizar para los embeddings |
| `encodingFormat` | string | No | El formato en el que se devolverán los embeddings |
| `apiKey` | string | Sí | Clave API de OpenAI |

#### Salida

| Parámetro | Tipo | Descripción |
| --------- | ---- | ----------- |
| `success` | boolean | Estado de éxito de la operación |
| `output` | object | Resultados de la generación de embeddings |

## Notas

- Categoría: `tools`
- Tipo: `openai`
