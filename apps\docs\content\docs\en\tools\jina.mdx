---
title: Jina
description: Convert website content into text
---

import { BlockInfoCard } from "@/components/ui/block-info-card"

<BlockInfoCard 
  type="jina"
  color="#333333"
  icon={true}
  iconSvg={`<svg className="block-icon"
      
      
      
      viewBox='0 0 30 14'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        d='M17.1516 5.25628C17.1724 5.25598 17.1932 5.25598 17.2146 5.25569C17.2831 5.2551 17.3514 5.2554 17.4197 5.25598C17.468 5.25569 17.5161 5.25569 17.5644 5.2554C17.6655 5.2554 17.7665 5.25569 17.8679 5.25628C17.9956 5.25686 18.1237 5.25657 18.2514 5.25569C19.3043 5.251 20.25 5.39426 21.0642 6.12112C21.0958 6.14632 21.1275 6.17122 21.1594 6.19612C21.8277 6.7885 22.2088 7.68733 22.2704 8.56624C22.278 8.73762 22.2777 8.90901 22.2768 9.08069C22.2771 9.1346 22.2771 9.1885 22.2771 9.24241C22.2774 9.35432 22.2771 9.46594 22.2768 9.57786C22.2762 9.72083 22.2765 9.8638 22.2771 10.0068C22.2774 10.1178 22.2774 10.2285 22.2771 10.3393C22.2771 10.3923 22.2771 10.445 22.2774 10.4978C22.2774 10.571 22.2771 10.6446 22.2765 10.7181C22.2768 10.7395 22.2771 10.7612 22.2771 10.7831C22.2753 10.9405 22.2408 11.0615 22.1335 11.1789C21.9882 11.292 21.8977 11.3102 21.7163 11.3108C21.6862 11.3108 21.6862 11.3108 21.6551 11.3111C21.5874 11.3114 21.5198 11.3114 21.4521 11.3114C21.4035 11.3114 21.3551 11.3116 21.3065 11.3116C21.1746 11.3122 21.0428 11.3122 20.9107 11.3125C20.8284 11.3125 20.746 11.3125 20.6637 11.3128C20.4059 11.3131 20.1481 11.3134 19.8903 11.3137C19.5926 11.3137 19.2953 11.3143 18.9976 11.3152C18.7676 11.3157 18.5376 11.316 18.3077 11.316C18.1703 11.316 18.0329 11.3163 17.8954 11.3169C17.7663 11.3172 17.6368 11.3172 17.5076 11.3172C17.4601 11.3169 17.4129 11.3172 17.3655 11.3175C17.3007 11.3178 17.2357 11.3178 17.1709 11.3175C17.1349 11.3175 17.0986 11.3175 17.0614 11.3175C16.933 11.3034 16.8343 11.2621 16.7385 11.1748C16.6243 11.0278 16.6067 10.9246 16.6067 10.7436C16.6064 10.7111 16.6064 10.7111 16.6064 10.678C16.6061 10.605 16.6058 10.5321 16.6058 10.4594C16.6055 10.4073 16.6055 10.3551 16.6052 10.303C16.6046 10.1313 16.6043 9.95989 16.604 9.78821C16.6037 9.72932 16.6037 9.67014 16.6037 9.61126C16.6032 9.33382 16.6026 9.05637 16.6023 8.77893C16.602 8.45872 16.6014 8.1385 16.6002 7.81858C16.5994 7.57102 16.5988 7.32346 16.5988 7.07591C16.5988 6.92825 16.5985 6.7803 16.5976 6.63264C16.597 6.49348 16.597 6.35432 16.5973 6.21516C16.5973 6.16419 16.597 6.11321 16.5967 6.06223C16.5929 5.56565 16.5929 5.56565 16.7283 5.3887C16.8583 5.27737 16.9811 5.25657 17.1516 5.25628Z'
        fill='currentColor'
      />
      <path
        d='M28.4893 5.83187C28.5341 5.86966 28.5786 5.90745 28.6229 5.94554C28.6407 5.95931 28.6586 5.97337 28.6771 5.98773C29.2217 6.42161 29.5281 7.12093 29.6483 7.79124C29.6509 7.83665 29.6524 7.88206 29.6527 7.92777C29.6529 7.96761 29.6529 7.96761 29.6532 8.00804C29.6532 8.03704 29.6535 8.06575 29.6535 8.09534C29.6538 8.12611 29.6538 8.15657 29.6541 8.18821C29.6547 8.28929 29.655 8.39036 29.6553 8.49144C29.6553 8.52601 29.6556 8.56058 29.6556 8.59603C29.6562 8.77884 29.6568 8.96165 29.6571 9.14446C29.6573 9.33314 29.6582 9.52181 29.6594 9.71077C29.6603 9.85579 29.6606 10.0011 29.6606 10.1461C29.6609 10.2159 29.6612 10.2856 29.6617 10.355C29.6623 10.4523 29.6623 10.5498 29.662 10.6471C29.6626 10.6902 29.6626 10.6902 29.6632 10.7341C29.662 10.9002 29.6474 11.0025 29.5311 11.1311C29.3805 11.2661 29.2481 11.265 29.0556 11.2632C29.0257 11.2635 28.9958 11.2638 28.9654 11.2638C28.8669 11.2644 28.7685 11.2641 28.67 11.2638C28.6012 11.2638 28.5323 11.2638 28.4635 11.2641C28.3191 11.2641 28.1746 11.2641 28.0302 11.2635C27.8462 11.2626 27.6625 11.2632 27.4785 11.2638C27.3362 11.2644 27.1938 11.2641 27.0517 11.2638C26.9837 11.2638 26.916 11.2638 26.8484 11.2641C25.9759 11.2667 25.1834 11.0508 24.5488 10.4268C24.5201 10.3981 24.4914 10.3691 24.4627 10.3401C24.4384 10.3155 24.4138 10.2909 24.3889 10.2657C23.8404 9.68851 23.5985 8.90687 23.6087 8.12435C23.6301 7.32191 23.9899 6.59681 24.5506 6.03343C25.6158 5.02562 27.3318 4.91839 28.4893 5.83187Z'
        fill='currentColor'
      />
      <path
        d='M8.6422 5.41793C8.7591 5.5858 8.7424 5.76246 8.74093 5.95904C8.74122 5.99566 8.74123 6.03228 8.74123 6.07037C8.74152 6.17086 8.74122 6.27164 8.74093 6.37213C8.74064 6.47818 8.74064 6.58424 8.74064 6.69C8.74093 6.86842 8.74035 7.04713 8.74005 7.22554C8.73947 7.43004 8.73917 7.63482 8.73947 7.83961C8.73947 8.03765 8.73947 8.23599 8.73917 8.43404C8.73888 8.51783 8.73888 8.60133 8.73888 8.68511C8.73947 9.82125 8.63869 10.9436 7.85119 11.8339C7.82951 11.8588 7.80753 11.8837 7.78527 11.9095C7.72023 11.9831 7.65402 12.0551 7.58751 12.1269C7.57199 12.1442 7.55675 12.1618 7.54064 12.1796C6.93712 12.8277 5.99757 13.1886 5.12746 13.2276C5.10197 13.2291 5.07619 13.2302 5.04982 13.2314C4.98771 13.2346 4.92531 13.2373 4.8629 13.2402C4.86085 12.0302 4.86085 10.8203 4.86261 9.61031C4.86291 9.44918 4.8629 9.28804 4.8632 9.12691C4.86349 8.63941 4.86466 8.15191 4.86671 7.6647C4.86789 7.45552 4.86847 7.24634 4.86876 7.03717C4.86876 6.84058 4.86964 6.64371 4.87082 6.44713C4.8714 6.37506 4.8714 6.30299 4.8714 6.23092C4.87111 6.13248 4.87199 6.03404 4.87287 5.9356C4.87257 5.90718 4.87228 5.87877 4.87199 5.84947C4.87521 5.66051 4.91417 5.53306 5.03869 5.38863C5.1673 5.27232 5.31642 5.28756 5.48107 5.28756C5.51066 5.28726 5.54025 5.28697 5.57101 5.28668C5.66886 5.28551 5.76701 5.28521 5.86515 5.28463C5.93341 5.28404 6.00167 5.28345 6.06994 5.28287C6.2132 5.2817 6.35617 5.28082 6.49914 5.27994C6.68253 5.27906 6.86564 5.27759 7.04874 5.27584C7.18996 5.27467 7.33087 5.27349 7.47179 5.27261C7.53947 5.27203 7.60685 5.27174 7.67453 5.27115C7.76886 5.27027 7.86349 5.26998 7.95812 5.26939C7.98566 5.2691 8.01349 5.26881 8.0422 5.26881C8.4632 5.26734 8.4632 5.26734 8.6422 5.41793Z'
        fill='currentColor'
      />
      <path
        d='M11.2636 5.26714C11.2897 5.26685 11.3155 5.26685 11.3421 5.26655C11.3705 5.26655 11.3987 5.26626 11.428 5.26626C11.4578 5.26597 11.4877 5.26597 11.5185 5.26567C11.6175 5.26509 11.7165 5.26479 11.8158 5.2645C11.8665 5.26421 11.8665 5.26421 11.9184 5.26421C12.0974 5.26362 12.2767 5.26304 12.456 5.26274C12.6408 5.26245 12.8257 5.26157 13.0109 5.2604C13.1532 5.25952 13.2953 5.25923 13.4377 5.25923C13.506 5.25894 13.5742 5.25864 13.6422 5.25806C13.7377 5.25747 13.8332 5.25747 13.9287 5.25776C13.9569 5.25718 13.985 5.25688 14.0137 5.25659C14.1895 5.25776 14.3278 5.28501 14.4731 5.38872C14.6096 5.55659 14.6231 5.68052 14.6234 5.89233C14.6234 5.91343 14.6237 5.93481 14.6237 5.95679C14.6239 6.02798 14.6237 6.09917 14.6237 6.17036C14.6239 6.22134 14.6239 6.27261 14.6239 6.32358C14.6242 6.46216 14.6245 6.60103 14.6245 6.73989C14.6245 6.85562 14.6245 6.97134 14.6245 7.08735C14.6248 7.36069 14.6248 7.63403 14.6248 7.90708C14.6248 8.18921 14.6251 8.47105 14.6257 8.75288C14.626 8.99487 14.6263 9.23687 14.6263 9.47886C14.6263 9.62358 14.6263 9.76802 14.6266 9.91245C14.6269 10.0487 14.6269 10.1846 14.6266 10.3206C14.6266 10.3704 14.6266 10.4202 14.6269 10.47C14.6272 10.5382 14.6269 10.6065 14.6269 10.6745C14.6269 10.7128 14.6269 10.7509 14.6269 10.7902C14.616 10.9469 14.5935 11.0638 14.4895 11.1839C14.2952 11.3498 14.1092 11.3404 13.8655 11.3401C13.8233 11.3404 13.8233 11.3404 13.7802 11.3404C13.687 11.3407 13.5939 11.3407 13.501 11.341C13.436 11.341 13.3712 11.341 13.3065 11.3413C13.1705 11.3413 13.0346 11.3413 12.8987 11.3413C12.7249 11.3413 12.5509 11.3418 12.3772 11.3421C12.2433 11.3424 12.1091 11.3427 11.9752 11.3427C11.9114 11.3427 11.8472 11.3427 11.783 11.343C11.6934 11.3433 11.6034 11.3433 11.5138 11.343C11.4745 11.3433 11.4745 11.3433 11.4341 11.3436C11.2425 11.3424 11.0609 11.3348 10.9044 11.2132C10.7761 11.0486 10.7412 10.9103 10.7412 10.7035C10.7409 10.6821 10.7409 10.6607 10.7406 10.6387C10.7404 10.5672 10.7406 10.496 10.7409 10.4249C10.7409 10.3736 10.7406 10.322 10.7404 10.2708C10.7401 10.1319 10.7404 9.99272 10.7406 9.85386C10.7406 9.70796 10.7406 9.56235 10.7404 9.41675C10.7404 9.17183 10.7404 8.9272 10.7409 8.68257C10.7412 8.40015 10.7412 8.11743 10.7406 7.83472C10.7404 7.59185 10.7404 7.34897 10.7404 7.1061C10.7406 6.96108 10.7406 6.81636 10.7404 6.67134C10.7401 6.53511 10.7404 6.39858 10.7406 6.26235C10.7409 6.21255 10.7409 6.16245 10.7406 6.11265C10.7404 6.04409 10.7406 5.97583 10.7412 5.90757C10.7412 5.86919 10.7412 5.8311 10.7412 5.79185C10.7582 5.62397 10.7963 5.47515 10.9264 5.36118C11.0421 5.28325 11.1262 5.26802 11.2636 5.26714Z'
        fill='currentColor'
      />
      <path
        d='M3.58833 9.8857C3.97827 10.2715 4.18628 10.7596 4.20093 11.3066C4.18452 11.8662 3.96245 12.3628 3.56226 12.7527C3.194 13.0776 2.70064 13.2692 2.20523 13.2466C2.18326 13.2446 2.16158 13.2425 2.13902 13.2402C2.11031 13.2376 2.08159 13.2349 2.05201 13.232C1.51617 13.1658 1.08199 12.9168 0.732767 12.5078C0.370071 12.0132 0.294193 11.4762 0.364505 10.8783C0.476126 10.3738 0.798392 9.944 1.23081 9.66598C1.99693 9.22125 2.9148 9.30006 3.58833 9.8857Z'
        fill='currentColor'
      />
      <path
        d='M12.6714 0.74873C12.6995 0.748437 12.7273 0.748145 12.756 0.747559C13.2318 0.749609 13.693 0.952637 14.0349 1.28105C14.3838 1.6335 14.5947 2.0791 14.6 2.57627C14.5965 3.14111 14.4632 3.62246 14.063 4.04111C13.6912 4.40293 13.2008 4.59365 12.686 4.60859C12.1434 4.59453 11.6864 4.40234 11.2941 4.02969C10.9086 3.62305 10.7357 3.13232 10.7466 2.57598C10.7659 2.08145 10.9883 1.60625 11.3384 1.25791C11.7245 0.919824 12.1578 0.742578 12.6714 0.74873Z'
        fill='currentColor'
      />
    </svg>`}
/>

{/* MANUAL-CONTENT-START:intro */}
[Jina AI](https://jina.ai/) is a powerful content extraction tool that seamlessly integrates with Sim to transform web content into clean, readable text. This integration allows developers to easily incorporate web content processing capabilities into their agentic workflows.

Jina AI Reader specializes in extracting the most relevant content from web pages, removing clutter, advertisements, and formatting issues to produce clean, structured text that's optimized for language models and other text processing tasks.

With the Jina AI integration in Sim, you can:

- **Extract clean content** from any web page by simply providing a URL
- **Process complex web layouts** into structured, readable text
- **Maintain important context** while removing unnecessary elements
- **Prepare web content** for further processing in your agent workflows
- **Streamline research tasks** by quickly converting web information into usable data

This integration is particularly valuable for building agents that need to gather and process information from the web, conduct research, or analyze online content as part of their workflow.
{/* MANUAL-CONTENT-END */}


## Usage Instructions

Transform web content into clean, readable text using Jina AI's advanced extraction capabilities. Extract meaningful content from websites while preserving important information and optionally gathering links.



## Tools

### `jina_read_url`

Extract and process web content into clean, LLM-friendly text using Jina AI Reader. Supports advanced content parsing, link gathering, and multiple output formats with configurable processing options.

#### Input

| Parameter | Type | Required | Description |
| --------- | ---- | -------- | ----------- |
| `url` | string | Yes | The URL to read and convert to markdown |
| `useReaderLMv2` | boolean | No | Whether to use ReaderLM-v2 for better quality |
| `gatherLinks` | boolean | No | Whether to gather all links at the end |
| `jsonResponse` | boolean | No | Whether to return response in JSON format |
| `apiKey` | string | Yes | Your Jina AI API key |

#### Output

| Parameter | Type | Description |
| --------- | ---- | ----------- |
| `content` | string | The extracted content from the URL, processed into clean, LLM-friendly text |



## Notes

- Category: `tools`
- Type: `jina`
