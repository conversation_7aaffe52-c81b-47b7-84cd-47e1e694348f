---
title: S3
description: View S3 files
---

import { BlockInfoCard } from "@/components/ui/block-info-card"

<BlockInfoCard 
  type="s3"
  color="#E0E0E0"
  icon={true}
  iconSvg={`<svg className="block-icon"
    
    
    preserveAspectRatio='xMidYMid'
    viewBox='0 0 256 310'
    
    xmlns='http://www.w3.org/2000/svg'
  >
    <path d='m20.624 53.686-20.624 10.314v181.02l20.624 10.254.124-.149v-201.297z' fill='#8c3123' />
    <path d='m131 229-110.376 26.274v-201.588l110.376 25.701z' fill='#e05243' />
    <path d='m81.178 187.866 46.818 5.96.294-.678.263-76.77-.557-.6-46.818 5.874z' fill='#8c3123' />
    <path
      d='m127.996 229.295 107.371 26.035.169-.269-.003-201.195-.17-.18-107.367 25.996z'
      fill='#8c3123'
    />
    <path d='m174.827 187.866-46.831 5.96v-78.048l46.831 5.874z' fill='#e05243' />
    <path d='m174.827 89.631-46.831 8.535-46.818-8.535 46.759-12.256z' fill='#5e1f18' />
    <path d='m174.827 219.801-46.831-8.591-46.818 8.591 46.761 13.053z' fill='#f2b0a9' />
    <path
      d='m81.178 89.631 46.818-11.586.379-.117v-77.615l-.379-.313-46.818 23.413z'
      fill='#8c3123'
    />
    <path d='m174.827 89.631-46.831-11.586v-78.045l46.831 23.413z' fill='#e05243' />
    <path
      d='m127.996 309.428-46.823-23.405v-66.217l46.823 11.582.689.783-.187 75.906z'
      fill='#8c3123'
    />
    <g fill='#e05243'>
      <path d='m127.996 309.428 46.827-23.405v-66.217l-46.827 11.582z' />
      <path d='m235.367 53.686 20.633 10.314v181.02l-20.633 10.31z' />
    </g>
  </svg>`}
/>

{/* MANUAL-CONTENT-START:intro */}
[Amazon S3](https://aws.amazon.com/s3/) is a highly scalable, secure, and durable cloud storage service provided by Amazon Web Services. It's designed to store and retrieve any amount of data from anywhere on the web, making it one of the most widely used cloud storage solutions for businesses of all sizes.

With Amazon S3, you can:

- **Store unlimited data**: Upload files of any size and type with virtually unlimited storage capacity
- **Access from anywhere**: Retrieve your files from anywhere in the world with low-latency access
- **Ensure data durability**: Benefit from 99.999999999% (11 9's) durability with automatic data replication
- **Control access**: Manage permissions and access controls with fine-grained security policies
- **Scale automatically**: Handle varying workloads without manual intervention or capacity planning
- **Integrate seamlessly**: Connect with other AWS services and third-party applications easily
- **Optimize costs**: Choose from multiple storage classes to optimize costs based on access patterns

In Sim, the S3 integration enables your agents to retrieve and access files stored in your Amazon S3 buckets using secure presigned URLs. This allows for powerful automation scenarios such as processing documents, analyzing stored data, retrieving configuration files, and accessing media content as part of your workflows. Your agents can securely fetch files from S3 without exposing your AWS credentials, making it easy to incorporate cloud-stored assets into your automation processes. This integration bridges the gap between your cloud storage and AI workflows, enabling seamless access to your stored data while maintaining security best practices through AWS's robust authentication mechanisms.
{/* MANUAL-CONTENT-END */}


## Usage Instructions

Retrieve and view files from Amazon S3 buckets using presigned URLs.



## Tools

### `s3_get_object`

Retrieve an object from an AWS S3 bucket

#### Input

| Parameter | Type | Required | Description |
| --------- | ---- | -------- | ----------- |
| `accessKeyId` | string | Yes | Your AWS Access Key ID |
| `secretAccessKey` | string | Yes | Your AWS Secret Access Key |
| `s3Uri` | string | Yes | S3 Object URL |

#### Output

| Parameter | Type | Description |
| --------- | ---- | ----------- |
| `url` | string | Pre-signed URL for downloading the S3 object |
| `metadata` | object | File metadata including type, size, name, and last modified date |



## Notes

- Category: `tools`
- Type: `s3`
