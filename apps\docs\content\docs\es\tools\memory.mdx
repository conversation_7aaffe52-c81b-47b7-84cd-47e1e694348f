---
title: Memoria
description: <PERSON><PERSON><PERSON> almacén de memoria
---

import { BlockInfoCard } from "@/components/ui/block-info-card"

<BlockInfoCard 
  type="memory"
  color="#F64F9E"
  icon={true}
  iconSvg={`<svg className="block-icon"
      
      xmlns='http://www.w3.org/2000/svg'
      
      
      viewBox='0 0 24 24'
      fill='none'
      stroke='currentColor'
      strokeWidth='2'
      strokeLinecap='round'
      strokeLinejoin='round'
    >
      <path d='M12 5a3 3 0 1 0-5.997.125 4 4 0 0 0-2.526 5.77 4 4 0 0 0 .556 6.588A4 4 0 1 0 12 18Z' />
      <path d='M12 5a3 3 0 1 1 5.997.125 4 4 0 0 1 2.526 5.77 4 4 0 0 1-.556 6.588A4 4 0 1 1 12 18Z' />
      <path d='M15 13a4.5 4.5 0 0 1-3-4 4.5 4.5 0 0 1-3 4' />
      <path d='M17.599 6.5a3 3 0 0 0 .399-1.375' />
      <path d='M6.003 5.125A3 3 0 0 0 6.401 6.5' />
      <path d='M3.477 10.896a4 4 0 0 1 .585-.396' />
      <path d='M19.938 10.5a4 4 0 0 1 .585.396' />
      <path d='M6 18a4 4 0 0 1-1.967-.516' />
      <path d='M19.967 17.484A4 4 0 0 1 18 18' />
    </svg>`}
/>

## Instrucciones de uso

Crea almacenamiento persistente para datos que necesitan ser accedidos a través de múltiples pasos del flujo de trabajo. Almacena y recupera información a lo largo de la ejecución de tu flujo de trabajo para mantener el contexto y el estado.

## Herramientas

### `memory_add`

Añade una nueva memoria a la base de datos o agrega a una memoria existente con el mismo ID.

#### Entrada

| Parámetro | Tipo | Obligatorio | Descripción |
| --------- | ---- | ----------- | ----------- |
| `id` | string | Sí | Identificador para la memoria. Si ya existe una memoria con este ID, los nuevos datos se añadirán a ella. |
| `role` | string | Sí | Rol para la memoria del agente \(usuario, asistente o sistema\) |
| `content` | string | Sí | Contenido para la memoria del agente |

#### Salida

| Parámetro | Tipo | Descripción |
| --------- | ---- | ----------- |
| `success` | boolean | Indica si la memoria se añadió correctamente |
| `memories` | array | Array de objetos de memoria incluyendo la memoria nueva o actualizada |
| `error` | string | Mensaje de error si la operación falló |

### `memory_get`

Recuperar una memoria específica por su ID

#### Entrada

| Parámetro | Tipo | Obligatorio | Descripción |
| --------- | ---- | ----------- | ----------- |
| `id` | string | Sí | Identificador de la memoria a recuperar |

#### Salida

| Parámetro | Tipo | Descripción |
| --------- | ---- | ----------- |
| `success` | boolean | Indica si la memoria se recuperó correctamente |
| `memories` | array | Array de datos de memoria para el ID solicitado |
| `message` | string | Mensaje de éxito o error |
| `error` | string | Mensaje de error si la operación falló |

### `memory_get_all`

Recuperar todas las memorias de la base de datos

#### Entrada

| Parámetro | Tipo | Obligatorio | Descripción |
| --------- | ---- | ----------- | ----------- |

#### Salida

| Parámetro | Tipo | Descripción |
| --------- | ---- | ----------- |
| `success` | boolean | Indica si todas las memorias se recuperaron correctamente |
| `memories` | array | Array de todos los objetos de memoria con claves, tipos y datos |
| `message` | string | Mensaje de éxito o error |
| `error` | string | Mensaje de error si la operación falló |

### `memory_delete`

Eliminar una memoria específica por su ID

#### Entrada

| Parámetro | Tipo | Obligatorio | Descripción |
| --------- | ---- | ----------- | ----------- |
| `id` | string | Sí | Identificador de la memoria a eliminar |

#### Salida

| Parámetro | Tipo | Descripción |
| --------- | ---- | ----------- |
| `success` | boolean | Si la memoria fue eliminada con éxito |
| `message` | string | Mensaje de éxito o error |
| `error` | string | Mensaje de error si la operación falló |

## Notas

- Categoría: `blocks`
- Tipo: `memory`
