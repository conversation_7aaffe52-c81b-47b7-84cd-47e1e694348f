---
title: Connection Data Structure
---

import { Callout } from 'fumadocs-ui/components/callout'
import { Tab, Tabs } from 'fumadocs-ui/components/tabs'

When you connect blocks, understanding the data structure of different block outputs is important because the output data structure from the source block determines what values are available in the destination block. Each block type produces a specific output structure that you can reference in downstream blocks.

<Callout type="info">
  Understanding these data structures is essential for effectively using connection tags and
  accessing the right data in your workflows.
</Callout>

## Block Output Structures

Different block types produce different output structures. Here's what you can expect from each block type:

<Tabs items={['Agent Output', 'API Output', 'Function Output', 'Evaluator Output', 'Condition Output', 'Router Output']}>
  <Tab>
    ```json
    {
      "content": "The generated text response",
      "model": "gpt-4o",
      "tokens": {
        "prompt": 120,
        "completion": 85,
        "total": 205
      },
      "toolCalls": [...],
      "cost": [...],
      "usage": [...]
    }
    ```

    ### Agent Block Output Fields

    - **content**: The main text response generated by the agent
    - **model**: The AI model used (e.g., "gpt-4o", "claude-3-opus")
    - **tokens**: Token usage statistics
      - **prompt**: Number of tokens in the prompt
      - **completion**: Number of tokens in the completion
      - **total**: Total tokens used
    - **toolCalls**: Array of tool calls made by the agent (if any)
    - **cost**: Array of cost objects for each tool call (if any)
    - **usage**: Token usage statistics for the entire response

  </Tab>
  <Tab>
    ```json
    {
      "data": "Response data",
      "status": 200,
      "headers": {
        "content-type": "application/json",
        "cache-control": "no-cache"
      }
    }
    ```

    ### API Block Output Fields

    - **data**: The response data from the API (can be any type)
    - **status**: HTTP status code of the response
    - **headers**: HTTP headers returned by the API

  </Tab>
  <Tab>
    ```json
    {
      "result": "Function return value",
      "stdout": "Console output",
    }
    ```

    ### Function Block Output Fields

    - **result**: The return value of the function (can be any type)
    - **stdout**: Console output captured during function execution

  </Tab>
  <Tab>
    ```json
    {
      "content": "Evaluation summary",
      "model": "gpt-5",
      "tokens": {
        "prompt": 120,
        "completion": 85,
        "total": 205
      },
      "metric1": 8.5,
      "metric2": 7.2,
      "metric3": 9.0
    }
    ```

    ### Evaluator Block Output Fields

    - **content**: Summary of the evaluation
    - **model**: The AI model used for evaluation
    - **tokens**: Token usage statistics
    - **[metricName]**: Score for each metric defined in the evaluator (dynamic fields)

  </Tab>
  <Tab>
    ```json
    {
      "content": "Original content passed through",
      "conditionResult": true,
      "selectedPath": {
        "blockId": "2acd9007-27e8-4510-a487-73d3b825e7c1",
        "blockType": "agent",
        "blockTitle": "Follow-up Agent"
      },
      "selectedConditionId": "condition-1"
    }
    ```

    ### Condition Block Output Fields

    - **content**: The original content passed through
    - **conditionResult**: Boolean result of the condition evaluation
    - **selectedPath**: Information about the selected path
      - **blockId**: ID of the next block in the selected path
      - **blockType**: Type of the next block
      - **blockTitle**: Title of the next block
    - **selectedConditionId**: ID of the selected condition

  </Tab>
  <Tab>
    ```json
    {
      "content": "Routing decision",
      "model": "gpt-4o",
      "tokens": {
        "prompt": 120,
        "completion": 85,
        "total": 205
      },
      "selectedPath": {
        "blockId": "2acd9007-27e8-4510-a487-73d3b825e7c1",
        "blockType": "agent",
        "blockTitle": "Customer Service Agent"
      }
    }
    ```

    ### Router Block Output Fields

    - **content**: The routing decision text
    - **model**: The AI model used for routing
    - **tokens**: Token usage statistics
    - **selectedPath**: Information about the selected path
      - **blockId**: ID of the selected destination block
      - **blockType**: Type of the selected block
      - **blockTitle**: Title of the selected block

  </Tab>
</Tabs>

## Custom Output Structures

Some blocks may produce custom output structures based on their configuration:

1. **Agent Blocks with Response Format**: When using a response format in an Agent block, the output structure will match the defined schema instead of the standard structure.

2. **Function Blocks**: The `result` field can contain any data structure returned by your function code.

3. **API Blocks**: The `data` field will contain whatever the API returns, which could be any valid JSON structure.

<Callout type="warning">
  Always check the actual output structure of your blocks during development to ensure you're
  referencing the correct fields in your connections.
</Callout>

## Nested Data Structures

Many block outputs contain nested data structures. You can access these using dot notation in connection tags:

```
<blockName.path.to.nested.data>
```

For example:

- `<agent1.tokens.total>` - Access the total tokens from an Agent block
- `<api1.data.results[0].id>` - Access the ID of the first result from an API response
- `<function1.result.calculations.total>` - Access a nested field in a Function block's result
