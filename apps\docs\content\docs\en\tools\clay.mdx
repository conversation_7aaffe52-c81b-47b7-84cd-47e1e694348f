---
title: Clay
description: Populate Clay workbook
---

import { BlockInfoCard } from "@/components/ui/block-info-card"

<BlockInfoCard 
  type="clay"
  color="#E0E0E0"
  icon={true}
  iconSvg={`<svg className="block-icon"  xmlns='http://www.w3.org/2000/svg'   viewBox='0 0 400 400'>
      <path
        xmlns='http://www.w3.org/2000/svg'
        fill='#41B9FD'
        d=' M225.000000,1.000000   C227.042313,1.000000 229.084641,1.000000 231.903046,1.237045   
      C233.981308,1.648251 235.283447,1.974177 236.585678,1.974532   C276.426849,1.985374 316.268005,1.964254 356.349304,2.036658   
      C356.713806,2.242061 356.838165,2.358902 357.013062,2.696568   C357.361633,3.243123 357.659729,3.568854 358.029053,3.919451   
      C358.100250,3.944317 358.064270,4.090822 358.043335,4.397895   C358.300018,5.454089 358.577637,6.203210 358.919647,7.420082   
      C358.919891,27.877140 358.855774,47.866444 358.406097,67.910400   C355.200592,68.111740 352.380737,68.384270 349.560669,68.386124  
       C311.434967,68.411194 273.308777,68.303810 235.184082,68.495499   C229.321579,68.524979 223.465759,69.888084 217.280884,70.633224   
       C216.309952,70.742836 215.664993,70.853645 214.722351,70.824722   C211.834686,71.349052 209.244675,72.013123 206.377716,72.681381   
       C205.743713,72.776283 205.386673,72.866997 204.740524,72.831818   C198.868668,74.719879 193.285919,76.733833 187.518951,78.776100   
       C187.334747,78.804405 187.002716,78.975388 186.619080,78.955429   C183.339905,80.398605 180.444336,81.861732 177.450043,83.356339   
       C177.351318,83.387817 177.199478,83.528885 176.863098,83.476791   C174.940445,84.544197 173.354172,85.663696 171.490601,86.873726   
       C170.873749,87.151909 170.534180,87.339554 169.900208,87.480209   C169.065109,87.950676 168.524414,88.468132 167.772736,89.059799   
       C167.561722,89.134003 167.180191,89.367592 166.874084,89.344360   C166.036011,89.874809 165.504074,90.428497 164.768677,91.071411   
       C164.565247,91.160652 164.195068,91.406326 163.886719,91.361374   C162.847015,91.962418 162.115631,92.608421 161.328308,93.267891   
       C161.272369,93.281357 161.208405,93.377022 160.867157,93.365463   C158.692642,94.907082 156.859375,96.460266 154.780716,98.176086   
       C154.099411,98.731529 153.663513,99.124352 153.029877,99.558502   C152.562164,99.788048 152.505905,100.026695 152.411484,100.477333   
       C151.745850,101.065102 151.332077,101.491318 150.666687,101.980057   C150.244827,102.329651 150.074554,102.616714 149.702332,103.025635  
        C149.247330,103.342041 149.041901,103.578056 148.626404,103.921570   C148.191071,104.281303 148.013428,104.574989 147.660767,104.971512   
        C147.485733,105.074348 147.185501,105.347694 146.854645,105.346924   C145.509140,106.645203 144.494507,107.944252 143.328308,109.398895   
        C143.176773,109.554497 142.944397,109.921532 142.688324,109.990189   C142.263062,110.355179 142.093887,110.651512 141.672485,111.133896  
        C140.733337,112.108200 140.046402,112.896461 139.056610,113.710732   C138.269180,114.554047 137.784592,115.371346 137.263580,116.208557  
        C137.227158,116.228470 137.222885,116.311386 136.910522,116.418571   C134.917343,118.573212 133.067978,120.505791 131.581848,122.685951   
        C117.236908,143.729858 109.909592,167.062012 108.797867,192.458298   C106.874710,236.390839 120.176277,274.069336 154.210175,303.200592   
        C157.543198,306.053497 161.524918,308.148560 165.395065,310.715118   C165.584625,310.834839 166.004089,310.993286 166.112747,311.305908   
        C169.421280,313.480804 172.621170,315.343109 176.067993,317.436401   C196.154831,328.754059 217.585236,333.047546 240.138840,332.968475   
        C276.608368,332.840607 313.078613,332.912872 349.548553,332.932007   C352.369659,332.933472 355.190643,333.181519 358.042847,333.756317   
        C358.105377,352.504913 358.140625,370.812134 358.166443,389.119385   C358.179047,398.047455 357.157593,399.080383 348.101379,399.081543   
        C309.488556,399.086456 270.875702,399.088837 232.262939,399.034698   C229.118195,399.030304 225.976639,398.454163 222.828934,398.396088   
        C219.876633,398.341614 216.918152,398.621979 213.655640,398.750488   C212.946808,398.674561 212.544739,398.603149 211.932861,398.249359  
         C205.139450,396.920532 198.555878,395.874084 191.660583,394.785370   C190.959366,394.590973 190.569855,394.438812 189.976242,394.044556   
         C188.751892,393.631897 187.731628,393.461365 186.520462,393.271667   C186.329559,393.252502 185.966660,393.127686 185.711517,392.875610   
         C179.817810,390.901337 174.179230,389.179169 168.376038,387.422913   C168.211411,387.388824 167.919205,387.222443 167.713623,386.935791   
         C163.177170,384.926636 158.846298,383.204132 154.354828,381.442505   C154.194229,381.403320 153.913010,381.229431 153.720596,380.940063   
         C150.958603,379.507599 148.389023,378.364502 145.862350,377.112976   C145.905273,377.004486 145.834991,377.222992 145.696899,376.907410   
         C143.278778,375.470276 140.998734,374.348724 138.546249,373.152405   C138.373810,373.077606 138.071228,372.854553 137.964508,372.539856   
         C136.491272,371.591217 135.124771,370.957306 133.835419,370.230103   C133.912552,370.136810 133.731659,370.297668 133.638489,369.968719   
         C130.257477,367.557678 126.969620,365.475616 123.676697,363.365906   C123.671616,363.338226 123.618034,363.355438 123.527176,363.037048   
         C122.530983,362.219849 121.625641,361.721039 120.554291,361.141144   C120.388283,361.060028 120.099663,360.829254 120.012115,360.507904   
         C116.854935,357.864441 113.785301,355.542328 110.448624,353.088013   C109.480820,352.261383 108.780060,351.566956 108.005241,350.545807   
         C106.569366,349.183838 105.207550,348.148560 103.618164,346.953125   C102.887856,346.250793 102.385124,345.708649 101.851944,344.819275   
         C99.227608,341.972198 96.633736,339.472412 93.829559,336.814728   C93.315529,336.231140 93.011803,335.805389 92.626633,335.113678   
         C92.241318,334.653351 91.937447,334.458984 91.470352,334.116333   C91.113121,333.744141 90.954285,333.497589 90.815475,332.884094   
         C89.432999,331.125000 88.065689,329.710205 86.750458,328.261658   C86.802551,328.227905 86.679573,328.244812 86.625587,328.004700   
         C86.408173,327.453064 86.154968,327.258301 85.840820,327.092529   C85.869644,327.004852 85.792236,327.175934 85.788193,326.847412   
         C85.086029,325.775726 84.387909,325.032593 83.748154,324.192444   C83.806519,324.095428 83.656967,324.265442 83.677109,323.924805   
         C82.691200,322.493195 81.685143,321.402222 80.701370,320.271667   C80.723648,320.232025 80.638077,320.262756 80.664627,319.911865   
         C79.348137,317.824493 78.005081,316.088074 76.632942,314.335297   C76.603851,314.318970 76.610863,314.252594 76.569603,314.015747   
         C76.383919,313.466492 76.145622,313.265167 75.849998,313.133301   C75.886536,313.091675 75.786301,313.138794 75.787926,312.843567   
         C75.413757,312.136780 75.037964,311.725281 74.650452,311.296570   C74.638725,311.279388 74.605232,311.254669 74.648026,310.925659   
         C74.042847,309.802277 73.394867,309.007935 72.848984,308.101166   C72.951088,307.988739 72.736649,308.207153 72.749344,307.902405   
         C72.247162,307.034119 71.732277,306.470612 71.116684,305.727478   C71.015976,305.547882 70.879890,305.159210 70.904739,304.782593   
         C66.198082,293.805145 61.429871,283.220459 56.753250,272.595459   C54.901436,268.388306 53.253181,264.091522 51.402115,259.538025   
         C51.225922,258.823547 51.159870,258.406525 51.280235,257.681335   C50.130058,252.530197 48.793461,247.687271 47.372990,242.549011   
         C47.250717,241.846664 47.212318,241.439667 47.345688,240.702484   C46.854862,237.196991 46.192276,234.021698 45.439560,230.551788   
         C45.308647,229.849213 45.267864,229.441223 45.399055,228.679535   C45.646000,226.680176 45.810993,225.032898 45.781715,223.389099   
         C45.543224,209.998566 45.243523,196.609085 45.021889,183.218307   C44.965343,179.801880 45.121227,176.381912 45.183868,172.656006   
         C45.260223,171.945328 45.332214,171.542252 45.692661,170.944855   C46.379547,167.156143 46.777977,163.561768 47.196243,159.658173   
         C47.326954,158.952240 47.437832,158.555511 47.816860,157.951569   C48.405701,156.819183 48.802628,155.912750 49.035774,154.966003   
         C53.321564,137.562775 58.709690,120.561356 67.075592,104.614586   C68.431061,102.030846 69.442665,99.266708 70.700943,96.329689   
         C70.963600,95.758194 71.138519,95.442963 71.626465,95.023987   C72.881813,93.185463 73.824142,91.450684 74.833984,89.540924   
         C74.901497,89.365936 75.115746,89.058022 75.414856,88.950439   C76.055374,88.124435 76.396790,87.406006 76.808441,86.516800   
         C76.878685,86.346008 77.099190,86.049721 77.426208,85.968033   C78.773079,84.202591 79.792938,82.518845 80.906425,80.889481   
         C81.000053,80.943871 80.811523,80.846413 81.112083,80.718071   C81.899254,79.675362 82.385872,78.760994 82.980141,77.647797   
         C83.256111,77.193130 83.468399,76.981361 83.972061,76.695953   C84.379341,76.259384 84.539192,75.940521 84.777573,75.467239   
         C84.856110,75.312813 85.091125,75.058212 85.387177,74.957954   C86.071411,74.171829 86.459602,73.485962 86.959831,72.547165   
         C87.574921,71.763893 88.077972,71.233551 88.917511,70.614960   C90.438446,68.934166 91.622894,67.341637 92.892502,65.577087   
         C92.977646,65.405067 93.223930,65.110596 93.540451,65.035034   C94.925735,63.668842 95.994484,62.378204 97.037460,61.053047   
         C97.011688,61.018532 97.086418,61.061367 97.418701,60.997078   C100.387512,58.135143 103.024048,55.337498 105.840828,52.291214   
         C107.274651,50.972633 108.528229,49.902691 110.120842,48.821507   C111.324287,47.898228 112.188705,46.986183 113.028954,46.039188   
         C113.004784,46.004234 113.069771,46.059036 113.418266,46.038719   C115.379044,44.556744 116.991333,43.095085 118.618896,41.600952   
         C118.634186,41.568470 118.705971,41.569565 118.943619,41.531807   C119.496582,41.345333 119.686287,41.099613 119.875092,40.861622   
         C119.999825,40.966347 119.751175,40.750431 120.085175,40.695145   C121.552383,39.660774 122.685600,38.681686 123.971207,37.539024   
         C124.353516,37.180477 124.609665,37.030270 125.248093,36.934944   C127.105858,35.720867 128.607605,34.496674 130.284821,33.157169   
         C130.460281,33.041859 130.850082,32.885620 131.191956,32.879478   C132.720169,31.979248 133.906525,31.085161 135.242615,30.070633   
         C135.392365,29.950191 135.742935,29.792681 136.116943,29.797058   C144.044449,25.665834 151.597931,21.530237 159.443359,17.267967   
         C160.335373,16.929420 160.935471,16.717543 161.932648,16.610218   C166.284805,15.022083 170.239853,13.329394 174.481018,11.497526   
         C175.179947,11.265512 175.592758,11.172676 176.284058,11.232684   C181.045059,9.931384 185.527557,8.477241 190.283020,6.942632   
         C190.929428,6.798172 191.302902,6.734176 192.106628,6.758037   C200.661499,5.630559 208.799301,4.494970 216.903397,3.155535   
         C219.646088,2.702227 222.303574,1.733297 225.000000,1.000000  z'
      />
      <path
        xmlns='http://www.w3.org/2000/svg'
        fill='#CF207F'
        d=' M139.359467,113.684723   C140.046402,112.896461 140.733337,112.108200 141.935272,111.074768   
      C142.614975,110.526917 142.779678,110.224220 142.944397,109.921524   C142.944397,109.921532 143.176773,109.554497 143.635193,109.340279   
      C145.124252,107.866608 146.154877,106.607147 147.185501,105.347694   C147.185501,105.347694 147.485733,105.074348 147.925735,104.915680   
      C148.538528,104.456520 148.711319,104.156021 148.884109,103.855530   C149.041901,103.578056 149.247330,103.342041 149.974884,103.098984   
      C150.636948,103.055161 150.824478,103.059845 151.047058,103.134651   C151.082077,103.204781 151.296890,103.193550 151.296890,103.193550   
      C151.296890,103.193550 151.065384,103.011589 151.060242,102.733826   C151.009506,102.276550 150.963913,102.097046 150.918304,101.917534   
      C151.332077,101.491318 151.745850,101.065102 152.635773,100.460251   C153.111908,100.281609 153.497894,100.049179 153.789368,100.038872   
      C154.772659,99.452271 155.464478,98.875984 156.408234,98.117584   C157.490311,97.320854 158.320465,96.706223 159.411987,96.018272   
      C160.091385,95.613731 160.509415,95.282509 161.005707,94.693756   C161.125443,94.083160 161.166931,93.730095 161.208405,93.377022   
      C161.208405,93.377022 161.272369,93.281357 161.637833,93.283844   C162.733887,92.659668 163.464478,92.032997 164.195068,91.406326   
      C164.195068,91.406326 164.565247,91.160652 165.074371,91.083725   C166.115738,90.460403 166.647964,89.913994 167.180191,89.367592   
      C167.180191,89.367592 167.561722,89.134003 168.067535,89.083694   C169.113785,88.531319 169.654205,88.029266 170.194611,87.527206   
      C170.534180,87.339554 170.873749,87.151909 171.836243,86.913345   C174.039276,85.751251 175.619370,84.640068 177.199478,83.528885   
      C177.199478,83.528885 177.351318,83.387817 177.799438,83.385483   C179.820572,82.883362 181.393585,82.383591 183.170273,81.808777   
      C183.633362,81.599014 183.861649,81.423775 184.373871,81.123398   C185.491287,80.703987 186.293686,80.369202 187.361908,79.991440   
      C188.096588,79.696411 188.565445,79.444366 189.280243,79.140625   C189.689667,79.052353 189.853149,79.015762 190.210281,78.900085   
      C190.651642,78.688210 190.867310,78.515427 191.369507,78.235207   C192.110519,78.067825 192.532990,77.967896 193.244263,77.853729   
      C194.045349,77.588539 194.557632,77.337585 195.404114,77.018097   C196.821823,76.607903 197.905350,76.266235 199.266159,75.907867   
      C200.036407,75.656876 200.529373,75.422592 201.364365,75.106812   C202.827423,74.692017 203.948425,74.358734 205.380356,74.019363   
      C206.468277,73.766235 207.245285,73.519203 208.389984,73.226074   C209.493317,73.091133 210.228912,73.002289 211.290283,72.935577   
      C212.412201,72.683113 213.208344,72.408524 214.267502,72.100060   C214.705307,72.039871 214.880112,72.013565 215.424881,71.999588   
      C217.201248,71.734070 218.607666,71.456200 220.413910,71.153488   C221.880417,71.070969 222.947083,71.013298 224.279190,71.170303   
      C226.068039,70.992416 227.591461,70.599854 229.423401,70.196625   C230.143173,70.169228 230.554443,70.152512 231.313034,70.332619   
      C235.115021,70.382599 238.569687,70.235756 242.491425,70.087082   C280.953430,70.102844 318.948334,70.120430 357.053223,70.529343   
      C357.455536,73.045441 357.992554,75.169182 358.001373,77.295113   C358.070374,93.940338 358.043671,110.585976 358.034363,127.231491   
      C358.030548,134.046967 358.016937,134.057816 351.099701,134.059860   C310.817535,134.071823 270.534180,133.934753 230.254730,134.268967   
      C225.246338,134.310516 220.258575,136.842316 215.230850,138.283905   C215.200439,138.347610 215.065262,138.306870 214.806305,138.286804   
      C214.115921,138.505325 213.684479,138.743896 213.009598,139.115082   C212.583405,139.275208 212.400635,139.302734 211.833679,139.280731   
      C208.407166,140.913559 205.364853,142.595886 202.282257,144.308472   C202.241974,144.338730 202.168381,144.269897 201.973877,144.345428   
      C201.529541,144.568588 201.364868,144.781921 201.061798,145.322937   C200.647766,145.713150 200.457306,145.841385 199.948059,145.977448   
      C197.417572,147.954681 195.205872,149.924103 192.993881,151.942596   C192.993607,151.991669 192.895477,151.990555 192.549149,152.015503   
      C187.409988,154.769379 184.238312,158.680161 183.252487,164.111267   C183.188980,163.991821 183.294250,164.239044 182.950150,164.345627   
      C180.427338,169.367905 177.154861,174.103409 176.308884,179.238663   C174.781265,188.511490 174.320831,198.014923 174.115677,207.437317   
      C173.843521,219.937164 178.269516,231.196472 184.901489,241.604797   C185.796005,243.008667 187.567444,243.853790 188.990707,244.966980   
      C189.048599,244.976334 189.032700,245.092545 189.039658,245.443787   C189.760330,247.068161 190.225784,248.594147 191.225662,249.575775   
      C202.884888,261.022064 217.215424,267.483948 233.244598,267.746521   C272.873535,268.395599 312.520477,268.025818 352.159454,267.873199   
      C356.777344,267.855408 358.164368,269.300385 358.106323,273.876007   C357.865570,292.859802 357.967224,311.847900 357.480347,330.882874   
      C338.906525,330.962463 320.795410,331.052429 302.684601,331.010834   C276.765686,330.951324 250.846970,330.795715 224.637268,330.524200   
      C223.236160,330.268494 222.125992,330.169708 220.602966,330.058136   C219.095612,329.927734 218.001114,329.810120 216.705780,329.546783   
      C216.025055,329.282104 215.545151,329.163147 214.711487,329.008087   C213.887634,328.910431 213.417526,328.848877 212.660461,328.610291   
      C211.246506,328.304504 210.119537,328.175751 208.744629,328.011780   C208.333069,327.943604 208.169434,327.910645 207.938263,327.637787   
      C207.248444,327.303284 206.626129,327.208649 205.594803,327.076263   C204.102722,326.877716 203.019669,326.716858 201.800995,326.447266   
      C201.471100,326.205719 201.260620,326.107544 200.685684,325.968201   C199.212677,325.508331 198.087952,325.124298 196.745544,324.584839   
      C196.008286,324.314789 195.488724,324.200195 194.630951,324.040466   C193.850174,323.890259 193.407623,323.785156 192.841400,323.544250   
      C192.535934,323.239014 192.330688,323.105682 192.067078,322.987274   C192.032166,322.966125 191.968018,322.915680 191.729294,322.721558   
      C190.699036,322.352661 189.907501,322.177887 188.818344,321.917145   C188.322571,321.773010 188.124420,321.714844 187.806183,321.529083   
      C187.508530,321.243896 187.309464,321.121094 186.809235,320.966248   C186.343460,320.853546 186.157333,320.807709 185.820770,320.618958   
      C185.449020,320.300232 185.201187,320.178223 184.579239,320.017242   C183.123337,319.463867 182.015015,319.003296 180.807480,318.445465   
      C180.565079,318.228424 180.407501,318.132172 179.911469,317.900696   C178.706055,317.357391 177.824753,316.972839 176.813736,316.472290   
      C176.496887,316.208344 176.292038,316.091339 175.768234,315.863037   C174.296906,315.078705 173.126801,314.436676 171.834732,313.642029   
      C171.530289,313.298096 171.319397,313.146332 170.800644,312.938660   C170.334427,312.781097 170.147659,312.718903 169.839874,312.529358   
      C169.543640,312.242981 169.349289,312.112366 168.837830,311.854187   C167.694580,311.463196 166.849335,311.228241 166.004089,310.993286   
      C166.004089,310.993286 165.584625,310.834839 165.340561,310.390503   C163.548645,308.481201 162.131165,306.841003 160.433350,305.577545   
      C135.450775,286.986084 120.418205,262.047058 113.761909,231.918289   C110.147652,215.558807 109.790779,198.967697 111.782127,182.339249   
      C113.832611,165.216965 118.597160,148.944382 127.160858,133.886154   C130.497955,128.018265 133.867905,122.169083 137.222885,116.311386   
      C137.222885,116.311386 137.227158,116.228470 137.540863,116.214661   C138.211945,116.106445 138.569351,116.012032 139.062988,115.851028   
      C139.427094,115.546883 139.469406,115.275383 139.372986,114.756676   C139.495758,114.250427 139.475632,113.964195 139.359467,113.684723  z'
      />
      <path
        xmlns='http://www.w3.org/2000/svg'
        fill='#FFC947'
        d=' M200.266830,145.969620   C200.457306,145.841385 200.647766,145.713150 201.270264,145.275589   
      C201.994553,144.826004 202.149918,144.593887 202.168381,144.269897   C202.168381,144.269897 202.241974,144.338730 202.627762,144.274597   
      C206.081650,142.583710 209.149765,140.956970 212.217880,139.330231   C212.400635,139.302734 212.583405,139.275208 213.260132,139.131683   
      C214.191147,138.779388 214.628204,138.543121 215.065262,138.306854   C215.065262,138.306870 215.200439,138.347610 215.615753,138.262543   
      C222.236084,137.117767 228.435684,135.178802 234.646988,135.140549   C276.033936,134.885590 317.423431,135.036758 358.812073,135.055969   
      C358.822845,178.409409 358.833618,221.762833 358.350433,265.618347   C317.222778,266.132172 276.588776,266.228516 235.955917,266.054840   
      C230.533264,266.031647 225.031219,265.015839 219.714111,263.807587   C207.453613,261.021515 197.827393,253.684341 189.032700,245.092545   
      C189.032700,245.092545 189.048599,244.976334 188.932205,244.635071   C178.652054,231.033371 175.024597,215.782471 175.030136,199.385284   
      C175.034317,187.007950 178.389404,175.448639 183.294250,164.239044   C183.294250,164.239044 183.188980,163.991821 183.536774,163.962189   
      C186.888184,159.951889 189.891830,155.971222 192.895477,151.990555   C192.895477,151.990555 192.993607,151.991669 193.307098,151.842606   
      C195.835999,149.785568 198.051407,147.877594 200.266830,145.969620  z'
      />
    </svg>`}
/>

{/* MANUAL-CONTENT-START:intro */}
[Clay](https://www.clay.com/) is a data enrichment and workflow automation platform that helps teams streamline lead generation, research, and data operations through powerful integrations and flexible inputs.

Learn how to use the Clay Tool in Sim to seamlessly insert data into a Clay workbook through webhook triggers. This tutorial walks you through setting up a webhook, configuring data mapping, and automating real-time updates to your Clay workbooks. Perfect for streamlining lead generation and data enrichment directly from your workflow!

<iframe
  width="100%"
  height="400"
  src="https://www.youtube.com/embed/cx_75X5sI_s"
  title="Clay Integration with Sim"
  frameBorder="0"
  allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
  allowFullScreen
></iframe>

With Clay, you can:

- **Enrich agent outputs**: Automatically feed your Sim agent data into Clay tables for structured tracking and analysis
- **Trigger workflows via webhooks**: Use Clay’s webhook support to initiate Sim agent tasks from within Clay
- **Leverage data loops**: Seamlessly iterate over enriched data rows with agents that operate across dynamic datasets

In Sim, the Clay integration allows your agents to push structured data into Clay tables via webhooks. This makes it easy to collect, enrich, and manage dynamic outputs such as leads, research summaries, or action items—all in a collaborative, spreadsheet-like interface. Your agents can populate rows in real time, enabling asynchronous workflows where AI-generated insights are captured, reviewed, and used by your team. Whether you're automating research, enriching CRM data, or tracking operational outcomes, Clay becomes a living data layer that interacts intelligently with your agents. By connecting Sim with Clay, you gain a powerful way to operationalize agent results, loop over datasets with precision, and maintain a clean, auditable record of AI-driven work.
{/* MANUAL-CONTENT-END */}


## Usage Instructions

Populate Clay workbook with data using a JSON or plain text. Enables direct communication and notifications with channel confirmation.



## Tools

### `clay_populate`

Populate Clay with data from a JSON file. Enables direct communication and notifications with timestamp tracking and channel confirmation.

#### Input

| Parameter | Type | Required | Description |
| --------- | ---- | -------- | ----------- |
| `webhookURL` | string | Yes | The webhook URL to populate |
| `data` | json | Yes | The data to populate |
| `authToken` | string | Yes | Auth token for Clay webhook authentication |

#### Output

| Parameter | Type | Description |
| --------- | ---- | ----------- |
| `success` | boolean | Operation success status |
| `output` | json | Clay populate operation results including response data from Clay webhook |



## Notes

- Category: `tools`
- Type: `clay`
