---
title: Block Reference Syntax
description: How to reference data between blocks in YAML workflows
---

import { Callout } from 'fumadocs-ui/components/callout'
import { Tab, Tabs } from 'fumadocs-ui/components/tabs'

Block references are the foundation of data flow in Sim workflows. Understanding how to correctly reference outputs from one block as inputs to another is essential for building functional workflows.

## Basic Reference Rules

### 1. Use Block Names, Not Block IDs

<Tabs items={['Correct', 'Incorrect']}>
  <Tab>
    ```yaml
    # Block definition
    email-sender:
      type: agent
      name: "Email Generator"
      # ... configuration

    # Reference the block
    next-block:
      inputs:
        userPrompt: "Process this: <emailgenerator.content>"
    ```
  </Tab>
  <Tab>
    ```yaml
    # Block definition
    email-sender:
      type: agent
      name: "Email Generator"
      # ... configuration

    # ❌ Don't reference by block ID
    next-block:
      inputs:
        userPrompt: "Process this: <email-sender.content>"
    ```
  </Tab>
</Tabs>

### 2. Convert Names to Reference Format

To create a block reference:

1. **Take the block name**: "Email Generator"
2. **Convert to lowercase**: "email generator" 
3. **Remove spaces and special characters**: "emailgenerator"
4. **Add property**: `<emailgenerator.content>`

### 3. Use Correct Properties

Different block types expose different properties:

- **Agent blocks**: `.content` (the AI response)
- **Function blocks**: `.output` (the return value)
- **API blocks**: `.output` (the response data)
- **Tool blocks**: `.output` (the tool result)

## Reference Examples

### Common Block References

```yaml
# Agent block outputs
<agentname.content>           # Primary AI response
<agentname.tokens>            # Token usage information
<agentname.cost>              # Estimated cost
<agentname.tool_calls>        # Tool execution details

# Function block outputs  
<functionname.output>         # Function return value
<functionname.error>          # Error information (if any)

# API block outputs
<apiname.output>              # Response data
<apiname.status>              # HTTP status code
<apiname.headers>             # Response headers

# Tool block outputs
<toolname.output>             # Tool execution result
```

### Multi-Word Block Names

```yaml
# Block name: "Data Processor 2"
<dataprocessor2.output>

# Block name: "Email Validation Service"  
<emailvalidationservice.output>

# Block name: "Customer Info Agent"
<customerinfoagent.content>
```

## Special Reference Cases

### Starter Block

<Callout type="warning">
  The starter block is always referenced as `<start.input>` regardless of its actual name.
</Callout>

```yaml
# Starter block definition
my-custom-start:
  type: starter
  name: "Custom Workflow Start"
  # ... configuration

# Always reference as 'start'
agent-1:
  inputs:
    userPrompt: <start.input>  # ✅ Correct
    # userPrompt: <customworkflowstart.input>  # ❌ Wrong
```

### Loop Variables

Inside loop blocks, special variables are available:

```yaml
# Available in loop child blocks
<loop.index>          # Current iteration (0-based)
<loop.currentItem>    # Current item being processed (forEach loops)
<loop.items>          # Full collection (forEach loops)
```

### Parallel Variables

Inside parallel blocks, special variables are available:

```yaml
# Available in parallel child blocks
<parallel.index>          # Instance number (0-based)
<parallel.currentItem>    # Item for this instance
<parallel.items>          # Full collection
```

## Complex Reference Examples

### Nested Data Access

When referencing complex objects, use dot notation:

```yaml
# If an agent returns structured data
data-analyzer:
  type: agent
  name: "Data Analyzer"
  inputs:
    responseFormat: |
      {
        "schema": {
          "type": "object",
          "properties": {
            "analysis": {"type": "object"},
            "summary": {"type": "string"},
            "metrics": {"type": "object"}
          }
        }
      }

# Reference nested properties
next-step:
  inputs:
    userPrompt: |
      Summary: <dataanalyzer.analysis.summary>
      Score: <dataanalyzer.metrics.score>
      Full data: <dataanalyzer.content>
```

### Multiple References in Text

```yaml
email-composer:
  type: agent
  inputs:
    userPrompt: |
      Create an email with the following information:
      
      Customer: <customeragent.content>
      Order Details: <orderprocessor.output>
      Support Ticket: <ticketanalyzer.content>
      
      Original request: <start.input>
```

### References in Code Blocks

When using references in function blocks, they're replaced as JavaScript values:

```yaml
data-processor:
  type: function
  inputs:
    code: |
      // References are replaced with actual values
      const customerData = <customeragent.content>;
      const orderInfo = <orderprocessor.output>;
      const originalInput = <start.input>;
      
      // Process the data
      return {
        customer: customerData.name,
        orderId: orderInfo.id,
        processed: true
      };
```

## Reference Validation

Sim validates all references when importing YAML:

### Valid References
- Block exists in the workflow
- Property is appropriate for block type  
- No circular dependencies
- Proper syntax formatting

### Common Errors
- **Block not found**: Referenced block doesn't exist
- **Wrong property**: Using `.content` on a function block
- **Typos**: Misspelled block names or properties
- **Circular references**: Block references itself directly or indirectly

## Best Practices

1. **Use descriptive block names**: Makes references more readable
2. **Be consistent**: Use the same naming convention throughout
3. **Check references**: Ensure all referenced blocks exist
4. **Avoid deep nesting**: Keep reference chains manageable
5. **Document complex flows**: Add comments to explain reference relationships 