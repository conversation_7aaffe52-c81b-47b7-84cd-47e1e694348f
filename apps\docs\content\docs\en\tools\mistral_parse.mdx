---
title: Mistral Parser
description: Extract text from PDF documents
---

import { BlockInfoCard } from "@/components/ui/block-info-card"

<BlockInfoCard 
  type="mistral_parse"
  color="#000000"
  icon={true}
  iconSvg={`<svg className="block-icon"
      
      
      
      viewBox='1 0.5 24 22'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
      preserveAspectRatio='xMidYMid meet'
    >
      <g clipPath='url(#clip0_1621_58)'>
        <path d='M17.4541 0H21.8177V4.39481H17.4541V0Z' fill='black' />
        <path d='M19.6367 0H24.0003V4.39481H19.6367V0Z' fill='#F7D046' />
        <path
          d='M0 0H4.36359V4.39481H0V0ZM0 4.39481H4.36359V8.78961H0V4.39481ZM0 8.78971H4.36359V13.1845H0V8.78971ZM0 13.1845H4.36359V17.5793H0V13.1845ZM0 17.5794H4.36359V21.9742H0V17.5794Z'
          fill='black'
        />
        <path d='M2.18164 0H6.54523V4.39481H2.18164V0Z' fill='#F7D046' />
        <path
          d='M19.6362 4.39478H23.9998V8.78958H19.6362V4.39478ZM2.18164 4.39478H6.54523V8.78958H2.18164V4.39478Z'
          fill='#F2A73B'
        />
        <path d='M13.0908 4.39478H17.4544V8.78958H13.0908V4.39478Z' fill='black' />
        <path
          d='M15.2732 4.39478H19.6368V8.78958H15.2732V4.39478ZM6.5459 4.39478H10.9095V8.78958H6.5459V4.39478Z'
          fill='#F2A73B'
        />
        <path
          d='M10.9096 8.78979H15.2732V13.1846H10.9096V8.78979ZM15.2732 8.78979H19.6368V13.1846H15.2732V8.78979ZM6.5459 8.78979H10.9096V13.1846H6.5459V8.78979Z'
          fill='#EE792F'
        />
        <path d='M8.72754 13.1846H13.0911V17.5794H8.72754V13.1846Z' fill='black' />
        <path d='M10.9092 13.1846H15.2728V17.5794H10.9092V13.1846Z' fill='#EB5829' />
        <path
          d='M19.6362 8.78979H23.9998V13.1846H19.6362V8.78979ZM2.18164 8.78979H6.54523V13.1846H2.18164V8.78979Z'
          fill='#EE792F'
        />
        <path d='M17.4541 13.1846H21.8177V17.5794H17.4541V13.1846Z' fill='black' />
        <path d='M19.6367 13.1846H24.0003V17.5794H19.6367V13.1846Z' fill='#EB5829' />
        <path d='M17.4541 17.5793H21.8177V21.9742H17.4541V17.5793Z' fill='black' />
        <path d='M2.18164 13.1846H6.54523V17.5794H2.18164V13.1846Z' fill='#EB5829' />
        <path
          d='M19.6362 17.5793H23.9998V21.9742H19.6362V17.5793ZM2.18164 17.5793H6.54523V21.9742H2.18164V17.5793Z'
          fill='#EA3326'
        />
      </g>
      <defs>
        <clipPath id='clip0_1621_58'>
          <rect   fill='white' />
        </clipPath>
      </defs>
    </svg>`}
/>

{/* MANUAL-CONTENT-START:intro */}
The Mistral Parse tool provides a powerful way to extract and process content from PDF documents using [Mistral's OCR API](https://mistral.ai/). This tool leverages advanced optical character recognition to accurately extract text and structure from PDF files, making it easy to incorporate document data into your agent workflows.

With the Mistral Parse tool, you can:

- **Extract text from PDFs**: Accurately convert PDF content to text, markdown, or JSON formats
- **Process PDFs from URLs**: Directly extract content from PDFs hosted online by providing their URLs
- **Maintain document structure**: Preserve formatting, tables, and layout from the original PDFs
- **Extract images**: Optionally include embedded images from the PDFs
- **Select specific pages**: Process only the pages you need from multi-page documents

The Mistral Parse tool is particularly useful for scenarios where your agents need to work with PDF content, such as analyzing reports, extracting data from forms, or processing text from scanned documents. It simplifies the process of making PDF content available to your agents, allowing them to work with information stored in PDFs just as easily as with direct text input.
{/* MANUAL-CONTENT-END */}


## Usage Instructions

Extract text and structure from PDF documents using Mistral's OCR API. Either enter a URL to a PDF document or upload a PDF file directly. Configure processing options and get the content in your preferred format. For URLs, they must be publicly accessible and point to a valid PDF file. Note: Google Drive, Dropbox, and other cloud storage links are not supported; use a direct download URL from a web server instead.



## Tools

### `mistral_parser`

Parse PDF documents using Mistral OCR API

#### Input

| Parameter | Type | Required | Description |
| --------- | ---- | -------- | ----------- |
| `filePath` | string | Yes | URL to a PDF document to be processed |
| `fileUpload` | object | No | File upload data from file-upload component |
| `resultType` | string | No | Type of parsed result \(markdown, text, or json\). Defaults to markdown. |
| `includeImageBase64` | boolean | No | Include base64-encoded images in the response |
| `pages` | array | No | Specific pages to process \(array of page numbers, starting from 0\) |
| `imageLimit` | number | No | Maximum number of images to extract from the PDF |
| `imageMinSize` | number | No | Minimum height and width of images to extract from the PDF |
| `apiKey` | string | Yes | Mistral API key \(MISTRAL_API_KEY\) |

#### Output

| Parameter | Type | Description |
| --------- | ---- | ----------- |
| `success` | boolean | Whether the PDF was parsed successfully |
| `content` | string | Extracted content in the requested format \(markdown, text, or JSON\) |
| `metadata` | object | Processing metadata including jobId, fileType, pageCount, and usage info |



## Notes

- Category: `tools`
- Type: `mistral_parse`
