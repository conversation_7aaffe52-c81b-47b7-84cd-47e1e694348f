---
title: API
---

import { Callout } from 'fumadocs-ui/components/callout'
import { Step, Steps } from 'fumadocs-ui/components/steps'
import { Tab, Tabs } from 'fumadocs-ui/components/tabs'
import { Image } from '@/components/ui/image'

The API block enables you to connect your workflow to external services through API endpoints using HTTP requests. It supports various methods like GET, POST, PUT, DELETE, and PATCH, allowing you to interact with virtually any API endpoint.

<div className="flex justify-center">
  <Image
    src="/static/blocks/api.png"
    alt="API Block"
    width={500}
    height={400}
    className="my-6"
  />
</div>

## Overview

The API block enables you to:

<Steps>
  <Step>
    <strong>Connect to external services</strong>: Make HTTP requests to REST APIs and web services
  </Step>
  <Step>
    <strong>Send and receive data</strong>: Process responses and transform data from external sources
  </Step>
  <Step>
    <strong>Integrate third-party platforms</strong>: Connect with services like Stripe, Slack, or custom APIs
  </Step>
  <Step>
    <strong>Handle authentication</strong>: Support various auth methods including Bearer tokens and API keys
  </Step>
</Steps>

## How It Works

The API block processes HTTP requests through a structured approach:

1. **Configure Request** - Set URL, method, headers, and body parameters
2. **Execute Request** - Send HTTP request to the specified endpoint
3. **Process Response** - Handle response data, status codes, and headers
4. **Error Handling** - Manage timeouts, retries, and error conditions

## Configuration Options

### URL

The endpoint URL for the API request. This can be:

- A static URL entered directly in the block
- A dynamic URL connected from another block's output
- A URL with path parameters

### Method

Select the HTTP method for your request:

- **GET**: Retrieve data from the server
- **POST**: Send data to the server to create a resource
- **PUT**: Update an existing resource on the server
- **DELETE**: Remove a resource from the server
- **PATCH**: Partially update an existing resource

### Query Parameters

Define key-value pairs that will be appended to the URL as query parameters. For example:

```
Key: apiKey
Value: your_api_key_here

Key: limit
Value: 10
```

These would be added to the URL as `?apiKey=your_api_key_here&limit=10`.

### Headers

Configure HTTP headers for your request. Common headers include:

```
Key: Content-Type
Value: application/json

Key: Authorization
Value: Bearer your_token_here
```

### Request Body

For methods that support a request body (POST, PUT, PATCH), you can define the data to send. The body can be:

- JSON data entered directly in the block
- Data connected from another block's output
- Dynamically generated during workflow execution

### Accessing Results

After an API request completes, you can access its outputs:

- **`<api.data>`**: The response body data from the API
- **`<api.status>`**: HTTP status code (200, 404, 500, etc.)
- **`<api.headers>`**: Response headers from the server
- **`<api.error>`**: Error details if the request failed

## Advanced Features

### Dynamic URL Construction

Build URLs dynamically using variables from previous blocks:

```javascript
// In a Function block before the API
const userId = <start.userId>;
const apiUrl = `https://api.example.com/users/${userId}/profile`;
```

### Request Retries

The API block automatically handles:
- Network timeouts with exponential backoff
- Rate limit responses (429 status codes)
- Server errors (5xx status codes) with retry logic
- Connection failures with reconnection attempts

### Response Validation

Validate API responses before processing:

```javascript
// In a Function block after the API
if (<api.status> === 200) {
  const data = <api.data>;
  // Process successful response
} else {
  // Handle error response
  console.error(`API Error: ${<api.status>}`);
}
```

## Inputs and Outputs

<Tabs items={['Configuration', 'Variables', 'Results']}>
  <Tab>
    <ul className="list-disc space-y-2 pl-6">
      <li>
        <strong>URL</strong>: The endpoint to send the request to
      </li>
      <li>
        <strong>Method</strong>: HTTP method (GET, POST, PUT, DELETE, PATCH)
      </li>
      <li>
        <strong>Query Parameters</strong>: Key-value pairs for URL parameters
      </li>
      <li>
        <strong>Headers</strong>: HTTP headers for authentication and content type
      </li>
      <li>
        <strong>Body</strong>: Request payload for POST/PUT/PATCH methods
      </li>
    </ul>
  </Tab>
  <Tab>
    <ul className="list-disc space-y-2 pl-6">
      <li>
        <strong>api.data</strong>: Response body data from the API call
      </li>
      <li>
        <strong>api.status</strong>: HTTP status code returned by server
      </li>
      <li>
        <strong>api.headers</strong>: Response headers from the server
      </li>
      <li>
        <strong>api.error</strong>: Error details if request failed
      </li>
    </ul>
  </Tab>
  <Tab>
    <ul className="list-disc space-y-2 pl-6">
      <li>
        <strong>Response Data</strong>: Primary API response content
      </li>
      <li>
        <strong>Status Information</strong>: HTTP status and error details
      </li>
      <li>
        <strong>Access</strong>: Available in blocks after the API call
      </li>
    </ul>
  </Tab>
</Tabs>

## Example Use Cases

### Fetch User Profile Data

<div className="mb-4 rounded-md border p-4">
  <h4 className="font-medium">Scenario: Retrieve user information from external service</h4>
  <ol className="list-decimal pl-5 text-sm">
    <li>Function block constructs user ID from input</li>
    <li>API block calls GET /users/&#123;id&#125; endpoint</li>
    <li>Function block processes and formats user data</li>
    <li>Response block returns formatted profile</li>
  </ol>
</div>

### Payment Processing

<div className="mb-4 rounded-md border p-4">
  <h4 className="font-medium">Scenario: Process payment through Stripe API</h4>
  <ol className="list-decimal pl-5 text-sm">
    <li>Function block validates payment data</li>
    <li>API block creates payment intent via Stripe</li>
    <li>Condition block handles payment success/failure</li>
    <li>Supabase block updates order status in database</li>
  </ol>
</div>

## Best Practices

- **Use environment variables for sensitive data**: Don't hardcode API keys or credentials
- **Handle errors gracefully**: Connect error handling logic for failed requests
- **Validate responses**: Check status codes and response formats before processing data
- **Respect rate limits**: Be mindful of API rate limits and implement appropriate throttling
