---
title: OneDrive
description: <PERSON><PERSON><PERSON>, télécharger et lister des fichiers
---

import { BlockInfoCard } from "@/components/ui/block-info-card"

<BlockInfoCard 
  type="onedrive"
  color="#E0E0E0"
  icon={true}
  iconSvg={`<svg className="block-icon"  fill='currentColor' viewBox='0 0 32 32' xmlns='http://www.w3.org/2000/svg'>
      <g>
        <path
          d='M12.20245,11.19292l.00031-.0011,6.71765,4.02379,4.00293-1.68451.00018.00068A6.4768,6.4768,0,0,1,25.5,13c.14764,0,.29358.0067.43878.01639a10.00075,10.00075,0,0,0-18.041-3.01381C7.932,10.00215,7.9657,10,8,10A7.96073,7.96073,0,0,1,12.20245,11.19292Z'
          fill='#0364b8'
        />
        <path
          d='M12.20276,11.19182l-.00031.0011A7.96073,7.96073,0,0,0,8,10c-.0343,0-.06805.00215-.10223.00258A7.99676,7.99676,0,0,0,1.43732,22.57277l5.924-2.49292,2.63342-1.10819,5.86353-2.46746,3.06213-1.28859Z'
          fill='#0078d4'
        />
        <path
          d='M25.93878,13.01639C25.79358,13.0067,25.64764,13,25.5,13a6.4768,6.4768,0,0,0-2.57648.53178l-.00018-.00068-4.00293,1.68451,1.16077.69528L23.88611,18.19l1.66009.99438,5.67633,3.40007a6.5002,6.5002,0,0,0-5.28375-9.56805Z'
          fill='#1490df'
        />
        <path
          d='M25.5462,19.18437,23.88611,18.19l-3.80493-2.2791-1.16077-.69528L15.85828,16.5042,9.99475,18.97166,7.36133,20.07985l-5.924,2.49292A7.98889,7.98889,0,0,0,8,26H25.5a6.49837,6.49837,0,0,0,5.72253-3.41556Z'
          fill='#28a8ea'
        />
      </g>
    </svg>`}
/>

{/* MANUAL-CONTENT-START:intro */}
[OneDrive](https://onedrive.live.com) est le service de stockage cloud et de synchronisation de fichiers de Microsoft qui permet aux utilisateurs de stocker, d'accéder et de partager des fichiers en toute sécurité sur différents appareils. Profondément intégré à l'écosystème Microsoft 365, OneDrive prend en charge la collaboration transparente, le contrôle de version et l'accès en temps réel au contenu pour les équipes et les organisations.

Découvrez comment intégrer l'outil OneDrive dans Sim pour extraire, gérer et organiser automatiquement vos fichiers cloud dans vos flux de travail. Ce tutoriel vous guide à travers la connexion à OneDrive, la configuration de l'accès aux fichiers et l'utilisation du contenu stocké pour alimenter l'automatisation. Idéal pour synchroniser des documents et médias essentiels avec vos agents en temps réel.

Avec OneDrive, vous pouvez :

- **Stocker des fichiers en toute sécurité dans le cloud** : télécharger et accéder à des documents, des images et d'autres fichiers depuis n'importe quel appareil
- **Organiser votre contenu** : créer des dossiers structurés et gérer facilement les versions de fichiers
- **Collaborer en temps réel** : partager des fichiers, les modifier simultanément avec d'autres et suivre les modifications
- **Accéder depuis différents appareils** : utiliser OneDrive depuis un ordinateur, un mobile ou une plateforme web
- **S'intégrer à Microsoft 365** : travailler de manière transparente avec Word, Excel, PowerPoint et Teams
- **Contrôler les autorisations** : partager des fichiers et des dossiers avec des paramètres d'accès personnalisés et des contrôles d'expiration

Dans Sim, l'intégration OneDrive permet à vos agents d'interagir directement avec votre stockage cloud. Les agents peuvent télécharger de nouveaux fichiers dans des dossiers spécifiques, récupérer et lire des fichiers existants, et lister le contenu des dossiers pour organiser et accéder dynamiquement aux informations. Cette intégration permet à vos agents d'incorporer des opérations sur fichiers dans des flux de travail intelligents — automatisant la réception de documents, l'analyse de contenu et la gestion structurée du stockage. En connectant Sim avec OneDrive, vous donnez à vos agents la possibilité de gérer et d'utiliser des documents cloud de manière programmatique, éliminant les étapes manuelles et améliorant l'automatisation grâce à un accès sécurisé aux fichiers en temps réel.
{/* MANUAL-CONTENT-END */}

## Instructions d'utilisation

Intégrez les fonctionnalités OneDrive pour gérer les fichiers et dossiers. Téléchargez de nouveaux fichiers, créez de nouveaux dossiers et listez le contenu des dossiers en utilisant l'authentification OAuth. Prend en charge les opérations sur les fichiers avec des types MIME personnalisés et l'organisation des dossiers.

## Outils

### `onedrive_upload`

Télécharger un fichier vers OneDrive

#### Entrée

| Paramètre | Type | Obligatoire | Description |
| --------- | ---- | -------- | ----------- |
| `fileName` | chaîne | Oui | Le nom du fichier à télécharger |
| `content` | chaîne | Oui | Le contenu du fichier à télécharger |
| `folderSelector` | chaîne | Non | Sélectionnez le dossier où télécharger le fichier |
| `manualFolderId` | chaîne | Non | ID du dossier saisi manuellement \(mode avancé\) |

#### Sortie

| Paramètre | Type | Description |
| --------- | ---- | ----------- |
| `success` | booléen | Indique si le fichier a été téléchargé avec succès |
| `file` | objet | L'objet du fichier téléchargé avec métadonnées incluant id, nom, webViewLink, webContentLink et horodatages |

### `onedrive_create_folder`

Créer un nouveau dossier dans OneDrive

#### Entrée

| Paramètre | Type | Obligatoire | Description |
| --------- | ---- | -------- | ----------- |
| `folderName` | chaîne | Oui | Nom du dossier à créer |
| `folderSelector` | chaîne | Non | Sélectionnez le dossier parent où créer le dossier |
| `manualFolderId` | chaîne | Non | ID du dossier parent saisi manuellement \(mode avancé\) |

#### Sortie

| Paramètre | Type | Description |
| --------- | ---- | ----------- |
| `success` | booléen | Indique si le dossier a été créé avec succès |
| `file` | objet | L'objet du dossier créé avec métadonnées incluant id, nom, webViewLink et horodatages |

### `onedrive_list`

Lister les fichiers et dossiers dans OneDrive

#### Entrée

| Paramètre | Type | Obligatoire | Description |
| --------- | ---- | ----------- | ----------- |
| `folderSelector` | chaîne | Non | Sélectionner le dossier à partir duquel lister les fichiers |
| `manualFolderId` | chaîne | Non | L'ID du dossier saisi manuellement \(mode avancé\) |
| `query` | chaîne | Non | Une requête pour filtrer les fichiers |
| `pageSize` | nombre | Non | Le nombre de fichiers à renvoyer |

#### Sortie

| Paramètre | Type | Description |
| --------- | ---- | ----------- |
| `success` | booléen | Indique si les fichiers ont été listés avec succès |
| `files` | tableau | Tableau d'objets de fichiers et dossiers avec métadonnées |
| `nextPageToken` | chaîne | Jeton pour récupérer la page suivante de résultats \(facultatif\) |

## Remarques

- Catégorie : `tools`
- Type : `onedrive`
