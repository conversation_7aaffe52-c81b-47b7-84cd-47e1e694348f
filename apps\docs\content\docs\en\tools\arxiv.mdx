---
title: ArXiv
description: Search and retrieve academic papers from ArXiv
---

import { BlockInfoCard } from "@/components/ui/block-info-card"

<BlockInfoCard 
  type="arxiv"
  color="#E0E0E0"
  icon={true}
  iconSvg={`<svg className="block-icon"  id='logomark' xmlns='http://www.w3.org/2000/svg' viewBox='0 0 17.732 24.269'>
      <g id='tiny'>
        <path
          d='M573.549,280.916l2.266,2.738,6.674-7.84c.353-.47.52-.717.353-1.117a1.218,1.218,0,0,0-1.061-.748h0a.953.953,0,0,0-.712.262Z'
          transform='translate(-566.984 -271.548)'
          fill='#bdb9b4'
        />
        <path
          d='M579.525,282.225l-10.606-10.174a1.413,1.413,0,0,0-.834-.5,1.09,1.09,0,0,0-1.027.66c-.167.4-.047.681.319,1.206l8.44,10.242h0l-6.282,7.716a1.336,1.336,0,0,0-.323,1.3,1.114,1.114,0,0,0,1.04.69A.992.992,0,0,0,571,293l8.519-7.92A1.924,1.924,0,0,0,579.525,282.225Z'
          transform='translate(-566.984 -271.548)'
          fill='#b31b1b'
        />
        <path
          d='M584.32,293.912l-8.525-10.275,0,0L573.53,280.9l-1.389,1.254a2.063,2.063,0,0,0,0,2.965l10.812,10.419a.925.925,0,0,0,.742.282,1.039,1.039,0,0,0,.953-.667A1.261,1.261,0,0,0,584.32,293.912Z'
          transform='translate(-566.984 -271.548)'
          fill='#bdb9b4'
        />
      </g>
    </svg>`}
/>

{/* MANUAL-CONTENT-START:intro */}
[ArXiv](https://arxiv.org/) is a free, open-access repository of scientific research papers in fields such as physics, mathematics, computer science, quantitative biology, quantitative finance, statistics, electrical engineering, systems science, and economics. ArXiv provides a vast collection of preprints and published articles, making it a primary resource for researchers and practitioners worldwide.

With ArXiv, you can:

- **Search for academic papers**: Find research by keywords, author names, titles, categories, and more
- **Retrieve paper metadata**: Access abstracts, author lists, publication dates, and other bibliographic information
- **Download full-text PDFs**: Obtain the complete text of most papers for in-depth study
- **Explore author contributions**: View all papers by a specific author
- **Stay up-to-date**: Discover the latest submissions and trending topics in your field

In Sim, the ArXiv integration enables your agents to programmatically search, retrieve, and analyze scientific papers from ArXiv. This allows you to automate literature reviews, build research assistants, or incorporate up-to-date scientific knowledge into your agentic workflows. Use ArXiv as a dynamic data source for research, discovery, and knowledge extraction within your Sim projects.
{/* MANUAL-CONTENT-END */}


## Usage Instructions

Search for academic papers, retrieve metadata, download papers, and access the vast collection of scientific research on ArXiv.



## Tools

### `arxiv_search`

Search for academic papers on ArXiv by keywords, authors, titles, or other fields.

#### Input

| Parameter | Type | Required | Description |
| --------- | ---- | -------- | ----------- |
| `searchQuery` | string | Yes | The search query to execute |
| `searchField` | string | No | Field to search in: all, ti \(title\), au \(author\), abs \(abstract\), co \(comment\), jr \(journal\), cat \(category\), rn \(report number\) |
| `maxResults` | number | No | Maximum number of results to return \(default: 10, max: 2000\) |
| `sortBy` | string | No | Sort by: relevance, lastUpdatedDate, submittedDate \(default: relevance\) |
| `sortOrder` | string | No | Sort order: ascending, descending \(default: descending\) |

#### Output

| Parameter | Type | Description |
| --------- | ---- | ----------- |
| `papers` | json | Array of papers matching the search query |

### `arxiv_get_paper`

Get detailed information about a specific ArXiv paper by its ID.

#### Input

| Parameter | Type | Required | Description |
| --------- | ---- | -------- | ----------- |
| `paperId` | string | Yes | ArXiv paper ID \(e.g., "1706.03762"\) |

#### Output

| Parameter | Type | Description |
| --------- | ---- | ----------- |
| `paper` | json | Detailed information about the requested ArXiv paper |

### `arxiv_get_author_papers`

Search for papers by a specific author on ArXiv.

#### Input

| Parameter | Type | Required | Description |
| --------- | ---- | -------- | ----------- |
| `authorName` | string | Yes | Author name to search for |
| `maxResults` | number | No | Maximum number of results to return \(default: 10, max: 2000\) |

#### Output

| Parameter | Type | Description |
| --------- | ---- | ----------- |
| `authorPapers` | json | Array of papers authored by the specified author |



## Notes

- Category: `tools`
- Type: `arxiv`
