---
title: Sharepoint
description: <PERSON><PERSON> et créer des pages
---

import { BlockInfoCard } from "@/components/ui/block-info-card"

<BlockInfoCard 
  type="sharepoint"
  color="#E0E0E0"
  icon={true}
  iconSvg={`<svg className="block-icon"  fill='currentColor' viewBox='0 0 32 32' xmlns='http://www.w3.org/2000/svg'>
      <circle fill='#036C70' cx='16.31' cy='8.90' r='8.90' />
      <circle fill='#1A9BA1' cx='23.72' cy='17.05' r='8.15' />
      <circle fill='#37C6D0' cx='17.42' cy='24.83' r='6.30' />
      <path
        fill='#000000'
        opacity='0.1'
        d='M17.79,8.03v15.82c0,0.55-0.34,1.04-0.85,1.25c-0.16,0.07-0.34,0.10-0.51,0.10H11.13c-0.01-0.13-0.01-0.24-0.01-0.37c0-0.12,0-0.25,0.01-0.37c0.14-2.37,1.59-4.46,3.77-5.40v-1.38c-4.85-0.77-8.15-5.32-7.39-10.17c0.01-0.03,0.01-0.07,0.02-0.10c0.04-0.25,0.09-0.50,0.16-0.74h8.74c0.74,0,1.36,0.60,1.36,1.36z'
      />
      <path
        fill='#000000'
        opacity='0.2'
        d='M15.69,7.41H7.54c-0.82,4.84,2.43,9.43,7.27,10.25c0.15,0.02,0.29,0.05,0.44,0.06c-2.30,1.09-3.97,4.18-4.12,6.73c-0.01,0.12-0.02,0.25-0.01,0.37c0,0.13,0,0.24,0.01,0.37c0.01,0.25,0.05,0.50,0.10,0.74h4.47c0.55,0,1.04-0.34,1.25-0.85c0.07-0.16,0.10-0.34,0.10-0.51V8.77c0-0.75-0.61-1.36-1.36-1.36z'
      />
      <path
        fill='#000000'
        opacity='0.2'
        d='M15.69,7.41H7.54c-0.82,4.84,2.43,9.43,7.27,10.26c0.10,0.02,0.20,0.03,0.30,0.05c-2.22,1.17-3.83,4.26-3.97,6.75h4.56c0.75,0,1.35-0.61,1.36-1.36V8.77c0-0.75-0.61-1.36-1.36-1.36z'
      />
      <path
        fill='#000000'
        opacity='0.2'
        d='M14.95,7.41H7.54c-0.78,4.57,2.08,8.97,6.58,10.11c-1.84,2.43-2.27,5.61-2.58,7.22h3.82c0.75,0,1.35-0.61,1.36-1.36V8.77c0-0.75-0.61-1.36-1.36-1.36z'
      />
      <path
        fill='#008789'
        d='M1.36,7.41h13.58c0.75,0,1.36,0.61,1.36,1.36v13.58c0,0.75-0.61,1.36-1.36,1.36H1.36c-0.75,0-1.36-0.61-1.36-1.36V8.77C0,8.02,0.61,7.41,1.36,7.41z'
      />
      <path
        fill='#FFFFFF'
        d='M6.07,15.42c-0.32-0.21-0.58-0.49-0.78-0.82c-0.19-0.34-0.28-0.73-0.27-1.12c-0.02-0.53,0.16-1.05,0.50-1.46c0.36-0.41,0.82-0.71,1.34-0.87c0.59-0.19,1.21-0.29,1.83-0.28c0.82-0.03,1.63,0.08,2.41,0.34v1.71c-0.34-0.20-0.71-0.35-1.09-0.44c-0.42-0.10-0.84-0.15-1.27-0.15c-0.45-0.02-0.90,0.08-1.31,0.28c-0.31,0.14-0.52,0.44-0.52,0.79c0,0.21,0.08,0.41,0.22,0.56c0.17,0.18,0.37,0.32,0.59,0.42c0.25,0.12,0.62,0.29,1.11,0.49c0.05,0.02,0.11,0.04,0.16,0.06c0.49,0.19,0.96,0.42,1.40,0.69c0.34,0.21,0.62,0.49,0.83,0.83c0.21,0.39,0.31,0.82,0.30,1.26c0.02,0.54-0.14,1.08-0.47,1.52c-0.33,0.40-0.77,0.69-1.26,0.85c-0.58,0.18-1.19,0.27-1.80,0.26c-0.55,0-1.09-0.04-1.63-0.13c-0.45-0.07-0.90-0.20-1.32-0.39v-1.80c0.40,0.29,0.86,0.50,1.34,0.64c0.48,0.15,0.97,0.23,1.47,0.24c0.46,0.03,0.92-0.07,1.34-0.28c0.29-0.16,0.46-0.47,0.46-0.80c0-0.23-0.09-0.45-0.25-0.61c-0.20-0.20-0.44-0.36-0.69-0.48c-0.30-0.15-0.73-0.34-1.31-0.59C6.91,16.14,6.48,15.80,6.07,15.42z'
      />
    </svg>`}
/>

{/* MANUAL-CONTENT-START:intro */}
[SharePoint](https://www.microsoft.com/en-us/microsoft-365/sharepoint/collaboration) est une plateforme collaborative de Microsoft qui permet aux utilisateurs de créer et de gérer des sites web internes, de partager des documents et d'organiser les ressources d'équipe. Elle offre une solution puissante et flexible pour créer des espaces de travail numériques et rationaliser la gestion de contenu dans les organisations.

Avec SharePoint, vous pouvez :

- **Créer des sites d'équipe et de communication** : Mettre en place des pages et des portails pour faciliter la collaboration, les annonces et la distribution de contenu
- **Organiser et partager du contenu** : Stocker des documents, gérer des fichiers et activer le contrôle de version avec des fonctionnalités de partage sécurisées
- **Personnaliser les pages** : Ajouter des parties textuelles pour adapter chaque site aux besoins de votre équipe
- **Améliorer la découvrabilité** : Utiliser les métadonnées, la recherche et les outils de navigation pour aider les utilisateurs à trouver rapidement ce dont ils ont besoin
- **Collaborer en toute sécurité** : Contrôler l'accès grâce à des paramètres d'autorisation robustes et à l'intégration avec Microsoft 365

Dans Sim, l'intégration SharePoint permet à vos agents de créer et d'accéder aux sites et pages SharePoint dans le cadre de leurs flux de travail. Cela permet une gestion automatisée des documents, le partage des connaissances et la création d'espaces de travail sans effort manuel. Les agents peuvent générer de nouvelles pages de projet, télécharger ou récupérer des fichiers et organiser dynamiquement les ressources en fonction des entrées du flux de travail. En connectant Sim à SharePoint, vous intégrez la collaboration structurée et la gestion de contenu dans vos flux d'automatisation — donnant à vos agents la capacité de coordonner les activités d'équipe, de mettre en évidence les informations clés et de maintenir une source unique de vérité dans toute votre organisation.
{/* MANUAL-CONTENT-END */}

## Instructions d'utilisation

Intégrez les fonctionnalités de Sharepoint pour gérer les pages. Lisez et créez des pages, et listez les sites en utilisant l'authentification OAuth. Prend en charge les opérations de page avec des types MIME personnalisés et l'organisation des dossiers.

## Outils

### `sharepoint_create_page`

Créer une nouvelle page dans un site SharePoint

#### Entrée

| Paramètre | Type | Obligatoire | Description |
| --------- | ---- | -------- | ----------- |
| `siteId` | chaîne | Non | L'ID du site SharePoint \(usage interne\) |
| `siteSelector` | chaîne | Non | Sélectionner le site SharePoint |
| `pageName` | chaîne | Oui | Le nom de la page à créer |
| `pageTitle` | chaîne | Non | Le titre de la page \(par défaut, le nom de la page si non fourni\) |
| `pageContent` | chaîne | Non | Le contenu de la page |

#### Sortie

| Paramètre | Type | Description |
| --------- | ---- | ----------- |
| `page` | objet | Informations sur la page SharePoint créée |

### `sharepoint_read_page`

Lire une page spécifique d'un site SharePoint

#### Entrée

| Paramètre | Type | Obligatoire | Description |
| --------- | ---- | -------- | ----------- |
| `siteSelector` | chaîne | Non | Sélectionner le site SharePoint |
| `siteId` | chaîne | Non | L'ID du site SharePoint \(usage interne\) |
| `pageId` | chaîne | Non | L'ID de la page à lire |
| `pageName` | chaîne | Non | Le nom de la page à lire \(alternative à pageId\) |
| `maxPages` | nombre | Non | Nombre maximum de pages à retourner lors de la liste de toutes les pages \(par défaut : 10, max : 50\) |

#### Sortie

| Paramètre | Type | Description |
| --------- | ---- | ----------- |
| `page` | objet | Informations sur la page SharePoint |

### `sharepoint_list_sites`

Lister les détails de tous les sites SharePoint

#### Entrée

| Paramètre | Type | Obligatoire | Description |
| --------- | ---- | -------- | ----------- |
| `siteSelector` | chaîne | Non | Sélectionner le site SharePoint |
| `groupId` | chaîne | Non | L'ID de groupe pour accéder à un site d'équipe de groupe |

#### Sortie

| Paramètre | Type | Description |
| --------- | ---- | ----------- |
| `site` | objet | Informations sur le site SharePoint actuel |

## Remarques

- Catégorie : `tools`
- Type : `sharepoint`
