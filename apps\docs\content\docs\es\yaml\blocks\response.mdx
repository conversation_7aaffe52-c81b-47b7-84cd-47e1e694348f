---
title: Esquema YAML del bloque de respuesta
description: Referencia de configuración YAML para bloques de respuesta
---

## Definición del esquema

```yaml
type: object
required:
  - type
  - name
properties:
  type:
    type: string
    enum: [response]
    description: Block type identifier
  name:
    type: string
    description: Display name for this response block
  inputs:
    type: object
    properties:
      dataMode:
        type: string
        enum: [structured, json]
        description: Mode for defining response data structure
        default: structured
      builderData:
        type: object
        description: Structured response data (when dataMode is 'structured')
      data:
        type: object
        description: JSON response data (when dataMode is 'json')
      status:
        type: number
        description: HTTP status code
        default: 200
        minimum: 100
        maximum: 599
      headers:
        type: array
        description: Response headers as table entries
        items:
          type: object
          properties:
            id:
              type: string
              description: Unique identifier for the header entry
            key:
              type: string
              description: Header name
            value:
              type: string
              description: Header value
            cells:
              type: object
              description: Cell display values for the table interface
              properties:
                Key:
                  type: string
                  description: Display value for the key column
                Value:
                  type: string
                  description: Display value for the value column
```

## Configuración de conexión

Los bloques de respuesta son bloques terminales (sin conexiones salientes) y definen la salida final:

```yaml
# No connections object needed - Response blocks are always terminal
```

## Ejemplos

### Respuesta simple

```yaml
simple-response:
  type: response
  name: "Simple Response"
  inputs:
    data:
      message: "Hello World"
      timestamp: <function.timestamp>
    status: 200
```

### Respuesta exitosa

```yaml
success-response:
  type: response
  name: "Success Response"
  inputs:
    data:
      success: true
      user:
        id: <agent.user_id>
        name: <agent.user_name>
        email: <agent.user_email>
      created_at: <function.timestamp>
    status: 201
    headers:
      - key: "Location"
        value: "/api/users/<agent.user_id>"
      - key: "X-Created-By"
        value: "workflow-engine"
```

### Respuesta con formato completo de encabezado de tabla

Cuando los encabezados se crean a través de la interfaz de tabla de la UI, el YAML incluye metadatos adicionales:

```yaml
api-response:
  type: response
  name: "API Response"
  inputs:
    data:
      message: "Request processed successfully"
      id: <agent.request_id>
    status: 200
    headers:
      - id: header-1-uuid-here
        key: "Content-Type"
        value: "application/json"
        cells:
          Key: "Content-Type"
          Value: "application/json"
      - id: header-2-uuid-here
        key: "Cache-Control"
        value: "no-cache"
        cells:
          Key: "Cache-Control"
          Value: "no-cache"
      - id: header-3-uuid-here
        key: "X-API-Version"
        value: "2.1"
        cells:
          Key: "X-API-Version"
          Value: "2.1"
```

### Respuesta de error

```yaml
error-response:
  type: response
  name: "Error Response"
  inputs:
    data:
      error: true
      message: <agent.error_message>
      code: "VALIDATION_FAILED"
      details: <function.validation_errors>
    status: 400
    headers:
      - key: "X-Error-Code"
        value: "VALIDATION_FAILED"
```

### Respuesta paginada

```yaml
paginated-response:
  type: response
  name: "Paginated Response"
  inputs:
    data:
      data: <agent.results>
      pagination:
        page: <start.page>
        per_page: <start.per_page>
        total: <function.total_count>
        total_pages: <function.total_pages>
    status: 200
    headers:
      - key: "X-Total-Count"
        value: <function.total_count>
      - key: "Cache-Control"
        value: "public, max-age=300"
      - key: "Content-Type"
        value: "application/json"
```

## Formatos de parámetros de tabla

El bloque de respuesta admite dos formatos para encabezados:

### Formato simplificado (YAML manual)

Al escribir YAML manualmente, puedes usar el formato simplificado:

```yaml
headers:
  - key: "Content-Type"
    value: "application/json"
  - key: "Cache-Control"
    value: "no-cache"
```

### Formato de tabla completo (generado por la UI)

Cuando los encabezados se crean a través de la interfaz de tabla de la UI, el YAML incluye metadatos adicionales:

```yaml
headers:
  - id: unique-identifier-here
    key: "Content-Type"
    value: "application/json"
    cells:
      Key: "Content-Type"
      Value: "application/json"
```

**Diferencias clave:**
- `id`: Identificador único para rastrear la fila de la tabla
- `cells`: Valores de visualización utilizados por la interfaz de tabla de la UI
- Ambos formatos son funcionalmente equivalentes para la ejecución del flujo de trabajo
- El formato completo preserva el estado de la UI al importar/exportar flujos de trabajo

**Importante:** Siempre coloca entre comillas los nombres de encabezados y valores que contengan caracteres especiales:

```yaml
headers:
  - id: content-type-uuid
    cells:
      Key: "Content-Type"
      Value: "application/json"
  - id: cache-control-uuid
    cells:
      Key: "Cache-Control" 
      Value: "no-cache"
```

```