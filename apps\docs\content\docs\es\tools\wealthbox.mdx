---
title: Wealthbox
description: Interactúa con Wealthbox
---

import { BlockInfoCard } from "@/components/ui/block-info-card"

<BlockInfoCard 
  type="wealthbox"
  color="#E0E0E0"
  icon={true}
  iconSvg={`<svg className="block-icon"
      
      xmlns='http://www.w3.org/2000/svg'
      version='1.0'
      
      
      viewBox='50 -50 200 200'
    >
      <g fill='#106ED4' stroke='none' transform='translate(0, 200) scale(0.15, -0.15)'>
        <path d='M764 1542 c-110 -64 -230 -134 -266 -156 -42 -24 -71 -49 -78 -65 -7 -19 -10 -126 -8 -334 3 -291 4 -307 23 -326 11 -11 103 -67 205 -126 102 -59 219 -127 261 -151 42 -24 85 -44 96 -44 23 0 527 288 561 320 22 22 22 23 22 340 0 288 -2 320 -17 338 -32 37 -537 322 -569 321 -18 0 -107 -46 -230 -117z m445 -144 c108 -62 206 -123 219 -135 22 -22 22 -26 22 -261 0 -214 -2 -242 -17 -260 -23 -26 -414 -252 -437 -252 -9 0 -70 31 -134 69 -64 37 -161 94 -215 125 l-97 57 2 261 3 261 210 123 c116 67 219 123 229 123 10 1 107 -50 215 -111z' />
        <path d='M700 1246 l-55 -32 -3 -211 -2 -211 37 -23 c21 -12 52 -30 69 -40 l30 -18 103 59 c56 33 109 60 117 60 8 0 62 -27 119 -60 l104 -60 63 37 c35 21 66 42 70 48 4 5 8 101 8 212 l0 202 -62 35 -63 35 -3 -197 c-1 -108 -6 -200 -11 -205 -5 -5 -54 17 -114 52 -58 34 -108 61 -111 61 -2 0 -51 -27 -107 -60 -56 -32 -106 -57 -111 -54 -4 3 -8 95 -8 205 0 109 -3 199 -7 199 -5 -1 -33 -16 -63 -34z' />
      </g>
    </svg>`}
/>

{/* MANUAL-CONTENT-START:intro */}
[Wealthbox](https://www.wealthbox.com/) es una plataforma CRM completa diseñada específicamente para asesores financieros y profesionales de gestión patrimonial. Proporciona un sistema centralizado para gestionar relaciones con clientes, seguimiento de interacciones y organización de flujos de trabajo empresariales en la industria de servicios financieros.

Con Wealthbox, puedes:

- **Gestionar relaciones con clientes**: Almacenar información detallada de contactos, datos de antecedentes e historiales de relaciones para todos tus clientes
- **Seguimiento de interacciones**: Crear y mantener notas sobre reuniones, llamadas y otros puntos de contacto con clientes
- **Organizar tareas**: Programar y gestionar actividades de seguimiento, fechas límite y elementos de acción importantes
- **Documentar flujos de trabajo**: Mantener registros completos de comunicaciones con clientes y procesos de negocio
- **Acceder a datos de clientes**: Recuperar información rápidamente con gestión organizada de contactos y capacidades de búsqueda
- **Automatizar seguimientos**: Establecer recordatorios y programar tareas para asegurar un compromiso constante con el cliente

En Sim, la integración de Wealthbox permite a tus agentes interactuar sin problemas con tus datos de CRM mediante autenticación OAuth. Esto permite potentes escenarios de automatización como la creación automática de notas de clientes a partir de transcripciones de reuniones, actualización de información de contacto, programación de tareas de seguimiento y recuperación de detalles de clientes para comunicaciones personalizadas. Tus agentes pueden leer notas existentes, contactos y tareas para entender el historial del cliente, mientras también crean nuevas entradas para mantener registros actualizados. Esta integración cierra la brecha entre tus flujos de trabajo de IA y la gestión de relaciones con clientes, permitiendo la entrada automatizada de datos, información inteligente sobre clientes y procesos administrativos optimizados que liberan tiempo para actividades más valiosas orientadas al cliente.
{/* MANUAL-CONTENT-END */}

## Instrucciones de uso

Integra la funcionalidad de Wealthbox para gestionar notas, contactos y tareas. Lee contenido de notas, contactos y tareas existentes y escribe en ellos usando autenticación OAuth. Compatible con manipulación de contenido de texto para creación y edición de notas.

## Herramientas

### `wealthbox_read_note`

Leer contenido de una nota de Wealthbox

#### Entrada

| Parámetro | Tipo | Obligatorio | Descripción |
| --------- | ---- | ----------- | ----------- |
| `noteId` | string | No | El ID de la nota a leer |

#### Salida

| Parámetro | Tipo | Descripción |
| --------- | ---- | ----------- |
| `success` | boolean | Estado de éxito de la operación |
| `output` | object | Datos y metadatos de la nota |

### `wealthbox_write_note`

Crear o actualizar una nota de Wealthbox

#### Entrada

| Parámetro | Tipo | Obligatorio | Descripción |
| --------- | ---- | ----------- | ----------- |
| `content` | string | Sí | El cuerpo principal de la nota |
| `contactId` | string | No | ID del contacto para vincular a esta nota |

#### Salida

| Parámetro | Tipo | Descripción |
| --------- | ---- | ----------- |
| `success` | boolean | Estado de éxito de la operación |
| `output` | object | Datos y metadatos de la nota creada o actualizada |

### `wealthbox_read_contact`

Leer contenido de un contacto de Wealthbox

#### Entrada

| Parámetro | Tipo | Obligatorio | Descripción |
| --------- | ---- | ----------- | ----------- |
| `contactId` | string | No | El ID del contacto a leer |

#### Salida

| Parámetro | Tipo | Descripción |
| --------- | ---- | ----------- |
| `success` | boolean | Estado de éxito de la operación |
| `output` | object | Datos y metadatos del contacto |

### `wealthbox_write_contact`

Crear un nuevo contacto en Wealthbox

#### Entrada

| Parámetro | Tipo | Obligatorio | Descripción |
| --------- | ---- | -------- | ----------- |
| `firstName` | string | Sí | El nombre del contacto |
| `lastName` | string | Sí | El apellido del contacto |
| `emailAddress` | string | No | La dirección de correo electrónico del contacto |
| `backgroundInformation` | string | No | Información de antecedentes sobre el contacto |

#### Salida

| Parámetro | Tipo | Descripción |
| --------- | ---- | ----------- |
| `success` | boolean | Estado de éxito de la operación |
| `output` | object | Datos y metadatos del contacto creado o actualizado |

### `wealthbox_read_task`

Leer contenido de una tarea de Wealthbox

#### Entrada

| Parámetro | Tipo | Obligatorio | Descripción |
| --------- | ---- | -------- | ----------- |
| `taskId` | string | No | El ID de la tarea a leer |

#### Salida

| Parámetro | Tipo | Descripción |
| --------- | ---- | ----------- |
| `success` | boolean | Estado de éxito de la operación |
| `output` | object | Datos y metadatos de la tarea |

### `wealthbox_write_task`

Crear o actualizar una tarea de Wealthbox

#### Entrada

| Parámetro | Tipo | Obligatorio | Descripción |
| --------- | ---- | -------- | ----------- |
| `title` | string | Sí | El nombre/título de la tarea |
| `dueDate` | string | Sí | La fecha y hora de vencimiento de la tarea \(formato: "AAAA-MM-DD HH:MM AM/PM -HHMM", p. ej., "2015-05-24 11:00 AM -0400"\) |
| `contactId` | string | No | ID del contacto para vincular a esta tarea |
| `description` | string | No | Descripción o notas sobre la tarea |

#### Salida

| Parámetro | Tipo | Descripción |
| --------- | ---- | ----------- |
| `success` | boolean | Estado de éxito de la operación |
| `output` | object | Datos y metadatos de la tarea creada o actualizada |

## Notas

- Categoría: `tools`
- Tipo: `wealthbox`
