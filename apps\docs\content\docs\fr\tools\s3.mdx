---
title: S3
description: <PERSON><PERSON><PERSON><PERSON> les fichiers S3
---

import { BlockInfoCard } from "@/components/ui/block-info-card"

<BlockInfoCard 
  type="s3"
  color="#E0E0E0"
  icon={true}
  iconSvg={`<svg className="block-icon"
    
    
    preserveAspectRatio='xMidYMid'
    viewBox='0 0 256 310'
    
    xmlns='http://www.w3.org/2000/svg'
  >
    <path d='m20.624 53.686-20.624 10.314v181.02l20.624 10.254.124-.149v-201.297z' fill='#8c3123' />
    <path d='m131 229-110.376 26.274v-201.588l110.376 25.701z' fill='#e05243' />
    <path d='m81.178 187.866 46.818 5.96.294-.678.263-76.77-.557-.6-46.818 5.874z' fill='#8c3123' />
    <path
      d='m127.996 229.295 107.371 26.035.169-.269-.003-201.195-.17-.18-107.367 25.996z'
      fill='#8c3123'
    />
    <path d='m174.827 187.866-46.831 5.96v-78.048l46.831 5.874z' fill='#e05243' />
    <path d='m174.827 89.631-46.831 8.535-46.818-8.535 46.759-12.256z' fill='#5e1f18' />
    <path d='m174.827 219.801-46.831-8.591-46.818 8.591 46.761 13.053z' fill='#f2b0a9' />
    <path
      d='m81.178 89.631 46.818-11.586.379-.117v-77.615l-.379-.313-46.818 23.413z'
      fill='#8c3123'
    />
    <path d='m174.827 89.631-46.831-11.586v-78.045l46.831 23.413z' fill='#e05243' />
    <path
      d='m127.996 309.428-46.823-23.405v-66.217l46.823 11.582.689.783-.187 75.906z'
      fill='#8c3123'
    />
    <g fill='#e05243'>
      <path d='m127.996 309.428 46.827-23.405v-66.217l-46.827 11.582z' />
      <path d='m235.367 53.686 20.633 10.314v181.02l-20.633 10.31z' />
    </g>
  </svg>`}
/>

{/* MANUAL-CONTENT-START:intro */}
[Amazon S3](https://aws.amazon.com/s3/) est un service de stockage cloud hautement évolutif, sécurisé et durable fourni par Amazon Web Services. Il est conçu pour stocker et récupérer n'importe quelle quantité de données depuis n'importe où sur le web, ce qui en fait l'une des solutions de stockage cloud les plus utilisées par les entreprises de toutes tailles.

Avec Amazon S3, vous pouvez :

- **Stocker des données illimitées** : télécharger des fichiers de toute taille et de tout type avec une capacité de stockage pratiquement illimitée
- **Accéder de n'importe où** : récupérer vos fichiers depuis n'importe où dans le monde avec un accès à faible latence
- **Assurer la durabilité des données** : bénéficier d'une durabilité de 99,999999999 % (11 neuf) avec réplication automatique des données
- **Contrôler l'accès** : gérer les permissions et les contrôles d'accès avec des politiques de sécurité précises
- **Évoluer automatiquement** : gérer des charges de travail variables sans intervention manuelle ni planification de capacité
- **S'intégrer facilement** : se connecter avec d'autres services AWS et applications tierces facilement
- **Optimiser les coûts** : choisir parmi plusieurs classes de stockage pour optimiser les coûts en fonction des modèles d'accès

Dans Sim, l'intégration S3 permet à vos agents de récupérer et d'accéder aux fichiers stockés dans vos buckets Amazon S3 en utilisant des URL présignées sécurisées. Cela permet des scénarios d'automatisation puissants tels que le traitement de documents, l'analyse de données stockées, la récupération de fichiers de configuration et l'accès à du contenu multimédia dans le cadre de vos flux de travail. Vos agents peuvent récupérer des fichiers depuis S3 en toute sécurité sans exposer vos identifiants AWS, ce qui facilite l'incorporation d'actifs stockés dans le cloud dans vos processus d'automatisation. Cette intégration comble le fossé entre votre stockage cloud et vos flux de travail IA, permettant un accès transparent à vos données stockées tout en maintenant les meilleures pratiques de sécurité grâce aux mécanismes d'authentification robustes d'AWS.
{/* MANUAL-CONTENT-END */}

## Instructions d'utilisation

Récupérer et visualiser des fichiers depuis des buckets Amazon S3 en utilisant des URL présignées.

## Outils

### `s3_get_object`

Récupérer un objet depuis un bucket AWS S3

#### Entrée

| Paramètre | Type | Obligatoire | Description |
| --------- | ---- | ---------- | ----------- |
| `accessKeyId` | chaîne | Oui | Votre ID de clé d'accès AWS |
| `secretAccessKey` | chaîne | Oui | Votre clé d'accès secrète AWS |
| `s3Uri` | chaîne | Oui | URL de l'objet S3 |

#### Sortie

| Paramètre | Type | Description |
| --------- | ---- | ----------- |
| `url` | chaîne | URL présignée pour télécharger l'objet S3 |
| `metadata` | objet | Métadonnées du fichier incluant le type, la taille, le nom et la date de dernière modification |

## Remarques

- Catégorie : `tools`
- Type : `s3`
