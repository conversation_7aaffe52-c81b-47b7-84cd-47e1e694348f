---
title: Microsoft Excel
description: <PERSON><PERSON>, escribir y actualizar datos
---

import { BlockInfoCard } from "@/components/ui/block-info-card"

<BlockInfoCard 
  type="microsoft_excel"
  color="#E0E0E0"
  icon={true}
  iconSvg={`<svg className="block-icon"
      
      xmlns='http://www.w3.org/2000/svg'
      version='1.1'
      id='Livello_1'
      x='0px'
      y='0px'
      viewBox='0 0 2289.75 2130'
      enableBackground='new 0 0 2289.75 2130'
    >
      <path
        fill='#185C37'
        d='M1437.75,1011.75L532.5,852v1180.393c0,53.907,43.7,97.607,97.607,97.607l0,0h1562.036  c53.907,0,97.607-43.7,97.607-97.607l0,0V1597.5L1437.75,1011.75z'
      />
      <path
        fill='#21A366'
        d='M1437.75,0H630.107C576.2,0,532.5,43.7,532.5,97.607c0,0,0,0,0,0V532.5l905.25,532.5L1917,1224.75  L2289.75,1065V532.5L1437.75,0z'
      />
      <path fill='#107C41' d='M532.5,532.5h905.25V1065H532.5V532.5z' />
      <path
        opacity='0.1'
        enableBackground='new'
        d='M1180.393,426H532.5v1331.25h647.893c53.834-0.175,97.432-43.773,97.607-97.607  V523.607C1277.825,469.773,1234.227,426.175,1180.393,426z'
      />
      <path
        opacity='0.2'
        enableBackground='new'
        d='M1127.143,479.25H532.5V1810.5h594.643  c53.834-0.175,97.432-43.773,97.607-97.607V576.857C1224.575,523.023,1180.977,479.425,1127.143,479.25z'
      />
      <path
        opacity='0.2'
        enableBackground='new'
        d='M1127.143,479.25H532.5V1704h594.643c53.834-0.175,97.432-43.773,97.607-97.607  V576.857C1224.575,523.023,1180.977,479.425,1127.143,479.25z'
      />
      <path
        opacity='0.2'
        enableBackground='new'
        d='M1073.893,479.25H532.5V1704h541.393c53.834-0.175,97.432-43.773,97.607-97.607  V576.857C1171.325,523.023,1127.727,479.425,1073.893,479.25z'
      />
      <linearGradient
        id='SVGID_1_'
        gradientUnits='userSpaceOnUse'
        x1='203.5132'
        y1='1729.0183'
        x2='967.9868'
        y2='404.9817'
        gradientTransform='matrix(1 0 0 -1 0 2132)'
      >
        <stop offset='0' style={{ stopColor: '#18884F' }} />
        <stop offset='0.5' style={{ stopColor: '#117E43' }} />
        <stop offset='1' style={{ stopColor: '#0B6631' }} />
      </linearGradient>
      <path
        fill='url(#SVGID_1_)'
        d='M97.607,479.25h976.285c53.907,0,97.607,43.7,97.607,97.607v976.285  c0,53.907-43.7,97.607-97.607,97.607H97.607C43.7,1650.75,0,1607.05,0,1553.143V576.857C0,522.95,43.7,479.25,97.607,479.25z'
      />
      <path
        fill='#FFFFFF'
        d='M302.3,1382.264l205.332-318.169L319.5,747.683h151.336l102.666,202.35  c9.479,19.223,15.975,33.494,19.49,42.919h1.331c6.745-15.336,13.845-30.228,21.3-44.677L725.371,747.79h138.929l-192.925,314.548  L869.2,1382.263H721.378L602.79,1160.158c-5.586-9.45-10.326-19.376-14.164-29.66h-1.757c-3.474,10.075-8.083,19.722-13.739,28.755  l-122.102,223.011H302.3z'
      />
      <path
        fill='#33C481'
        d='M2192.143,0H1437.75v532.5h852V97.607C2289.75,43.7,2246.05,0,2192.143,0L2192.143,0z'
      />
      <path fill='#107C41' d='M1437.75,1065h852v532.5h-852V1065z' />
    </svg>`}
/>

{/* MANUAL-CONTENT-START:intro */}
[Microsoft Teams](https://www.microsoft.com/en-us/microsoft-365/excel) es una potente aplicación de hojas de cálculo que permite la gestión, análisis y visualización de datos. A través de la integración de Microsoft Excel en Sim, puedes leer, escribir y manipular datos de hojas de cálculo de forma programática para satisfacer tus necesidades de automatización de flujos de trabajo.

Con la integración de Microsoft Excel, puedes:

- **Leer datos de hojas de cálculo**: Acceder a datos de rangos, hojas y celdas específicas
- **Escribir y actualizar datos**: Añadir nuevos datos o modificar contenido existente en hojas de cálculo
- **Gestionar tablas**: Crear y manipular estructuras de datos tabulares
- **Manejar múltiples hojas**: Trabajar con varias hojas de trabajo en un libro
- **Procesar datos**: Importar, exportar y transformar datos de hojas de cálculo

En Sim, la integración de Microsoft Excel proporciona acceso fluido a la funcionalidad de hojas de cálculo mediante autenticación OAuth. Puedes leer datos de rangos específicos, escribir nueva información, actualizar celdas existentes y manejar varios formatos de datos. La integración admite operaciones tanto de lectura como de escritura con opciones flexibles de entrada y salida. Esto te permite crear flujos de trabajo que pueden gestionar eficazmente datos de hojas de cálculo, ya sea extrayendo información para análisis, actualizando registros automáticamente o manteniendo la consistencia de datos en todas tus aplicaciones.
{/* MANUAL-CONTENT-END */}

## Instrucciones de uso

Integra la funcionalidad de Microsoft Excel para gestionar datos de hojas de cálculo. Lee datos de rangos específicos, escribe nuevos datos, actualiza celdas existentes y manipula datos de tablas utilizando autenticación OAuth. Admite varios formatos de entrada y salida para un manejo flexible de datos.

## Herramientas

### `microsoft_excel_read`

Leer datos de una hoja de cálculo de Microsoft Excel

#### Entrada

| Parámetro | Tipo | Obligatorio | Descripción |
| --------- | ---- | -------- | ----------- |
| `spreadsheetId` | string | Sí | El ID de la hoja de cálculo de la que leer |
| `range` | string | No | El rango de celdas del que leer. Acepta "NombreHoja!A1:B2" para rangos explícitos o simplemente "NombreHoja" para leer el rango utilizado de esa hoja. Si se omite, lee el rango utilizado de la primera hoja. |

#### Salida

| Parámetro | Tipo | Descripción |
| --------- | ---- | ----------- |
| `data` | object | Datos del rango de la hoja de cálculo |

### `microsoft_excel_write`

Escribir datos en una hoja de cálculo de Microsoft Excel

#### Entrada

| Parámetro | Tipo | Obligatorio | Descripción |
| --------- | ---- | -------- | ----------- |
| `spreadsheetId` | string | Sí | El ID de la hoja de cálculo en la que escribir |
| `range` | string | No | El rango de celdas en el que escribir |
| `values` | array | Sí | Los datos a escribir en la hoja de cálculo |
| `valueInputOption` | string | No | El formato de los datos a escribir |
| `includeValuesInResponse` | boolean | No | Si se deben incluir los valores escritos en la respuesta |

#### Salida

| Parámetro | Tipo | Descripción |
| --------- | ---- | ----------- |
| `updatedRange` | string | El rango que fue actualizado |
| `updatedRows` | number | Número de filas que fueron actualizadas |
| `updatedColumns` | number | Número de columnas que fueron actualizadas |
| `updatedCells` | number | Número de celdas que fueron actualizadas |
| `metadata` | object | Metadatos de la hoja de cálculo |

### `microsoft_excel_table_add`

Añadir nuevas filas a una tabla de Microsoft Excel

#### Entrada

| Parámetro | Tipo | Obligatorio | Descripción |
| --------- | ---- | ----------- | ----------- |
| `spreadsheetId` | string | Sí | El ID de la hoja de cálculo que contiene la tabla |
| `tableName` | string | Sí | El nombre de la tabla a la que añadir filas |
| `values` | array | Sí | Los datos para añadir a la tabla \(matriz de matrices o matriz de objetos\) |

#### Salida

| Parámetro | Tipo | Descripción |
| --------- | ---- | ----------- |
| `index` | number | Índice de la primera fila que fue añadida |
| `values` | array | Matriz de filas que fueron añadidas a la tabla |
| `metadata` | object | Metadatos de la hoja de cálculo |

## Notas

- Categoría: `tools`
- Tipo: `microsoft_excel`
