---
title: Typeform
description: Interactúa con Typeform
---

import { BlockInfoCard } from "@/components/ui/block-info-card"

<BlockInfoCard 
  type="typeform"
  color="#262627"
  icon={true}
  iconSvg={`<svg className="block-icon"
      
      version='1.1'
      id='Layer_1'
      xmlns='http://www.w3.org/2000/svg'
      xmlnsXlink='http://www.w3.org/1999/xlink'
      x='0px'
      y='0px'
      viewBox='0 0 122.3 80.3'
      xmlSpace='preserve'
    >
      <g>
        <path
          fill='currentColor'
          d='M94.3,0H65.4c-26,0-28,11.2-28,26.2l0,27.9c0,15.6,2,26.2,28.1,26.2h28.8c26,0,28-11.2,28-26.1V26.2
		C122.3,11.2,120.3,0,94.3,0z M0,20.1C0,6.9,5.2,0,14,0c8.8,0,14,6.9,14,20.1v40.1c0,13.2-5.2,20.1-14,20.1c-8.8,0-14-6.9-14-20.1
		V20.1z'
        />
      </g>
    </svg>`}
/>

{/* MANUAL-CONTENT-START:intro */}
[Typeform](https://www.typeform.com/) es una plataforma fácil de usar para crear formularios conversacionales, encuestas y cuestionarios con un enfoque en la experiencia atractiva del usuario.

Con Typeform, puedes:

- **Crear formularios interactivos**: Diseña formularios conversacionales atractivos que involucran a los encuestados con una interfaz única de una pregunta a la vez
- **Personalizar tu experiencia**: Utiliza lógica condicional, campos ocultos y temas personalizados para crear recorridos de usuario personalizados
- **Integrar con otras herramientas**: Conéctate con más de 1000 aplicaciones a través de integraciones nativas y APIs
- **Analizar datos de respuestas**: Obtén información procesable a través de herramientas completas de análisis e informes

En Sim, la integración de Typeform permite a tus agentes interactuar programáticamente con tus datos de Typeform como parte de sus flujos de trabajo. Los agentes pueden recuperar respuestas de formularios, procesar datos de envío e incorporar comentarios de usuarios directamente en los procesos de toma de decisiones. Esta integración es particularmente valiosa para escenarios como la calificación de leads, análisis de comentarios de clientes y personalización basada en datos. Al conectar Sim con Typeform, puedes crear flujos de trabajo de automatización inteligentes que transforman las respuestas de formularios en información procesable - analizando sentimientos, categorizando comentarios, generando resúmenes e incluso desencadenando acciones de seguimiento basadas en patrones específicos de respuesta.
{/* MANUAL-CONTENT-END */}

## Instrucciones de uso

Accede y recupera respuestas de tus formularios de Typeform. Integra datos de envíos de formularios en tu flujo de trabajo para análisis, almacenamiento o procesamiento.

## Herramientas

### `typeform_responses`

Recuperar respuestas de formularios de Typeform

#### Entrada

| Parámetro | Tipo | Obligatorio | Descripción |
| --------- | ---- | ----------- | ----------- |
| `formId` | string | Sí | ID del formulario de Typeform |
| `apiKey` | string | Sí | Token de acceso personal de Typeform |
| `pageSize` | number | No | Número de respuestas a recuperar \(predeterminado: 25\) |
| `since` | string | No | Recuperar respuestas enviadas después de esta fecha \(formato ISO 8601\) |
| `until` | string | No | Recuperar respuestas enviadas antes de esta fecha \(formato ISO 8601\) |
| `completed` | string | No | Filtrar por estado de finalización \(true/false\) |

#### Salida

| Parámetro | Tipo | Descripción |
| --------- | ---- | ----------- |
| `total_items` | number | Recuento total de respuestas |
| `page_count` | number | Recuento total de páginas |
| `items` | json | Elementos de respuesta |

### `typeform_files`

Descargar archivos subidos en respuestas de Typeform

#### Entrada

| Parámetro | Tipo | Obligatorio | Descripción |
| --------- | ---- | -------- | ----------- |
| `formId` | string | Sí | ID del formulario de Typeform |
| `responseId` | string | Sí | ID de respuesta que contiene los archivos |
| `fieldId` | string | Sí | ID único del campo de carga de archivos |
| `filename` | string | Sí | Nombre del archivo subido |
| `inline` | boolean | No | Si se debe solicitar el archivo con Content-Disposition en línea |
| `apiKey` | string | Sí | Token de acceso personal de Typeform |

#### Salida

| Parámetro | Tipo | Descripción |
| --------- | ---- | ----------- |
| `fileUrl` | string | URL de descarga directa para el archivo subido |
| `contentType` | string | Tipo MIME del archivo subido |
| `filename` | string | Nombre original del archivo subido |

### `typeform_insights`

Obtener información y análisis para formularios de Typeform

#### Entrada

| Parámetro | Tipo | Obligatorio | Descripción |
| --------- | ---- | -------- | ----------- |
| `formId` | string | Sí | ID del formulario de Typeform |
| `apiKey` | string | Sí | Token de acceso personal de Typeform |

#### Salida

| Parámetro | Tipo | Descripción |
| --------- | ---- | ----------- |
| `fields` | array | Número de usuarios que abandonaron en este campo |

## Notas

- Categoría: `tools`
- Tipo: `typeform`
