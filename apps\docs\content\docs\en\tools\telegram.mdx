---
title: Telegram
description: Send messages through Telegram or trigger workflows from Telegram events
---

import { BlockInfoCard } from "@/components/ui/block-info-card"

<BlockInfoCard 
  type="telegram"
  color="#E0E0E0"
  icon={true}
  iconSvg={`<svg className="block-icon"
      
      xmlns='http://www.w3.org/2000/svg'
      viewBox='0 0 24 24'
      
      
      fill='none'
    >
      <circle cx='12' cy='12' r='10' fill='#0088CC' />
      <path
        d='M16.7 8.4c.1-.6-.4-1.1-1-.8l-9.8 4.3c-.4.2-.4.8.1.9l2.1.7c.******* 1.1-.2l4.5-3.1c.1-.*******.2l-3.2 3.5c-.3.3-.2.8.2 1l3.6 2.3c.4.2.9-.1 1-.5l1.2-7.8Z'
        fill='white'
      />
    </svg>`}
/>

{/* MANUAL-CONTENT-START:intro */}
[Telegram](https://telegram.org) is a secure, cloud-based messaging platform that enables fast and reliable communication across devices and platforms. With over 700 million monthly active users, Telegram has established itself as one of the world's leading messaging services, known for its security, speed, and powerful API capabilities.

Telegram's Bot API provides a robust framework for creating automated messaging solutions and integrating communication features into applications. With support for rich media, inline keyboards, and custom commands, Telegram bots can facilitate sophisticated interaction patterns and automated workflows.

Learn how to create a webhook trigger in Sim that seamlessly initiates workflows from Telegram messages. This tutorial walks you through setting up a webhook, configuring it with Telegram's bot API, and triggering automated actions in real-time. Perfect for streamlining tasks directly from your chat!

<iframe
  width="100%"
  height="400"
  src="https://www.youtube.com/embed/9oKcJtQ0_IM"
  title="Use Telegram with Sim"
  frameBorder="0"
  allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
  allowFullScreen
></iframe>

Learn how to use the Telegram Tool in Sim to seamlessly automate message delivery to any Telegram group. This tutorial walks you through integrating the tool into your workflow, configuring group messaging, and triggering automated updates in real-time. Perfect for enhancing communication directly from your workspace!

<iframe
  width="100%"
  height="400"
  src="https://www.youtube.com/embed/AG55LpUreGI"
  title="Use Telegram with Sim"
  frameBorder="0"
  allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
  allowFullScreen
></iframe>

Key features of Telegram include:

- Secure Communication: End-to-end encryption and secure cloud storage for messages and media
- Bot Platform: Powerful bot API for creating automated messaging solutions and interactive experiences
- Rich Media Support: Send and receive messages with text formatting, images, files, and interactive elements
- Global Reach: Connect with users worldwide with support for multiple languages and platforms

In Sim, the Telegram integration enables your agents to leverage these powerful messaging capabilities as part of their workflows. This creates opportunities for automated notifications, alerts, and interactive conversations through Telegram's secure messaging platform. The integration allows agents to send messages programmatically to individuals or channels, enabling timely communication and updates. By connecting Sim with Telegram, you can build intelligent agents that engage users through a secure and widely-adopted messaging platform, perfect for delivering notifications, updates, and interactive communications.
{/* MANUAL-CONTENT-END */}


## Usage Instructions

Send messages to any Telegram channel using your Bot API key or trigger workflows from Telegram bot messages. Integrate automated notifications and alerts into your workflow to keep your team informed.



## Tools

### `telegram_message`

Send messages to Telegram channels or users through the Telegram Bot API. Enables direct communication and notifications with message tracking and chat confirmation.

#### Input

| Parameter | Type | Required | Description |
| --------- | ---- | -------- | ----------- |
| `botToken` | string | Yes | Your Telegram Bot API Token |
| `chatId` | string | Yes | Target Telegram chat ID |
| `text` | string | Yes | Message text to send |

#### Output

| Parameter | Type | Description |
| --------- | ---- | ----------- |
| `success` | boolean | Telegram message send success status |
| `messageId` | number | Unique Telegram message identifier |
| `chatId` | string | Target chat ID where message was sent |
| `text` | string | Text content of the sent message |
| `timestamp` | number | Unix timestamp when message was sent |
| `from` | object | Information about the bot that sent the message |



## Notes

- Category: `tools`
- Type: `telegram`
