---
title: Router
---

import { Callout } from 'fumadocs-ui/components/callout'
import { Step, Steps } from 'fumadocs-ui/components/steps'
import { Tab, Tabs } from 'fumadocs-ui/components/tabs'
import { Accordion, Accordions } from 'fumadocs-ui/components/accordion'
import { Image } from '@/components/ui/image'
import { Video } from '@/components/ui/video'

The Router block uses AI to intelligently decide which path your workflow should take next, routing workflow execution based on specific conditions or logic. Unlike Condition blocks that use simple rules, Router blocks can understand context and make smart routing decisions based on content analysis.

<div className="flex justify-center">
  <Image
    src="/static/blocks/router.png"
    alt="Router Block with Multiple Paths"
    width={500}
    height={400}
    className="my-6"
  />
</div>

## Overview

The Router block enables you to:

<Steps>
  <Step>
    <strong>Intelligent content routing</strong>: Use AI to understand intent and context
  </Step>
  <Step>
    <strong>Dynamic path selection</strong>: Route workflows based on unstructured content analysis
  </Step>
  <Step>
    <strong>Context-aware decisions</strong>: Make smart routing choices beyond simple rules
  </Step>
  <Step>
    <strong>Multi-path management</strong>: Handle complex workflows with multiple potential destinations
  </Step>
</Steps>

## Router vs Condition Blocks

<Accordions>
  <Accordion title="When to Use Router">
    - AI-powered content analysis needed
    - Unstructured or varying content types
    - Intent-based routing (e.g., "route support tickets to departments")
    - Context-aware decision making required
  </Accordion>
  <Accordion title="When to Use Condition">
    - Simple, rule-based decisions
    - Structured data or numeric comparisons
    - Fast, deterministic routing needed
    - Boolean logic sufficient
  </Accordion>
</Accordions>

## How It Works

The Router block:

<Steps>
  <Step>
    <strong>Analyze content</strong>: Uses an LLM to understand input content and context
  </Step>
  <Step>
    <strong>Evaluate targets</strong>: Compares content against available destination blocks
  </Step>
  <Step>
    <strong>Select destination</strong>: Identifies the most appropriate path based on intent
  </Step>
  <Step>
    <strong>Route execution</strong>: Directs workflow to the selected block
  </Step>
</Steps>

## Configuration Options

### Content/Prompt

The content or prompt that the Router will analyze to make routing decisions. This can be:

- A direct user query or input
- Output from a previous block
- A system-generated message

### Target Blocks

The possible destination blocks that the Router can select from. The Router will automatically detect connected blocks, but you can also:

- Customize the descriptions of target blocks to improve routing accuracy
- Specify routing criteria for each target block
- Exclude certain blocks from being considered as routing targets

### Model Selection

Choose an AI model to power the routing decision:

**OpenAI**: GPT-4o, o1, o3, o4-mini, gpt-4.1  \
**Anthropic**: Claude 3.7 Sonnet \
**Google**: Gemini 2.5 Pro, Gemini 2.0 Flash \
**Other Providers**: Groq, Cerebras, xAI, DeepSeek \
**Local Models**: Any model running on Ollama 

<div className="w-full max-w-2xl mx-auto overflow-hidden rounded-lg">
  <Video src="router-model-dropdown.mp4" width={500} height={350} />
</div>

**Recommendation**: Use models with strong reasoning capabilities like GPT-4o or Claude 3.7 Sonnet for more accurate routing decisions.

### API Key

Your API key for the selected LLM provider. This is securely stored and used for authentication.

### Accessing Results

After a router makes a decision, you can access its outputs:

- **`<router.prompt>`**: Summary of the routing prompt used
- **`<router.selected_path>`**: Details of the chosen destination block
- **`<router.tokens>`**: Token usage statistics from the LLM
- **`<router.cost>`**: Cost summary for the routing call (input, output, total)
- **`<router.model>`**: The model used for decision-making

## Advanced Features

### Custom Routing Criteria

Define specific criteria for each target block:

```javascript
// Example routing descriptions
Target Block 1: "Technical support issues, API problems, integration questions"
Target Block 2: "Billing inquiries, subscription changes, payment issues"
Target Block 3: "General questions, feedback, feature requests"
```

## Inputs and Outputs

<Tabs items={['Configuration', 'Variables']}>
  <Tab>
    <ul className="list-disc space-y-2 pl-6">
      <li>
        <strong>Content/Prompt</strong>: Text to analyze for routing decisions
      </li>
      <li>
        <strong>Target Blocks</strong>: Connected blocks as potential destinations
      </li>
      <li>
        <strong>Model</strong>: AI model for routing analysis
      </li>
      <li>
        <strong>API Key</strong>: Authentication for selected LLM provider
      </li>
    </ul>
  </Tab>
  <Tab>
    <ul className="list-disc space-y-2 pl-6">
      <li>
        <strong>router.prompt</strong>: Summary of routing prompt used
      </li>
      <li>
        <strong>router.selected_path</strong>: Details of chosen destination
      </li>
      <li>
        <strong>router.tokens</strong>: Token usage statistics
      </li>
      <li>
        <strong>router.cost</strong>: Cost summary for the routing call (input, output, total)
      </li>
      <li>
        <strong>router.model</strong>: Model used for decision-making
      </li>
    </ul>
  </Tab>
</Tabs>

## Example Use Cases

### Customer Support Triage

<div className="mb-4 rounded-md border p-4">
  <h4 className="font-medium">Scenario: Route support tickets to specialized departments</h4>
  <ol className="list-decimal pl-5 text-sm">
    <li>User submits support request via form</li>
    <li>Router analyzes ticket content and context</li>
    <li>Technical issues → Engineering support agent</li>
    <li>Billing questions → Finance support agent</li>
  </ol>
</div>

### Content Classification

<div className="mb-4 rounded-md border p-4">
  <h4 className="font-medium">Scenario: Classify and route user-generated content</h4>
  <ol className="list-decimal pl-5 text-sm">
    <li>User submits content or feedback</li>
    <li>Router analyzes content type and sentiment</li>
    <li>Feature requests → Product team workflow</li>
    <li>Bug reports → Technical support workflow</li>
  </ol>
</div>

### Lead Qualification

<div className="mb-4 rounded-md border p-4">
  <h4 className="font-medium">Scenario: Route leads based on qualification criteria</h4>
  <ol className="list-decimal pl-5 text-sm">
    <li>Lead information captured from form</li>
    <li>Router analyzes company size, industry, and needs</li>
    <li>Enterprise leads → Sales team with custom pricing</li>
    <li>SMB leads → Self-service onboarding flow</li>
  </ol>
</div>


## Best Practices

- **Provide clear target descriptions**: Help the Router understand when to select each destination with specific, detailed descriptions
- **Use specific routing criteria**: Define clear conditions and examples for each path to improve accuracy
- **Implement fallback paths**: Connect a default destination for when no specific path is appropriate
- **Test with diverse inputs**: Ensure the Router handles various input types, edge cases, and unexpected content
- **Monitor routing performance**: Review routing decisions regularly and refine criteria based on actual usage patterns
- **Choose appropriate models**: Use models with strong reasoning capabilities for complex routing decisions
